package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AddServicesRequest {
    private String transactionId;
    private List<Offer> offers;
    private String currency;
    private String culture;

    // Constructeur par défaut
    public AddServicesRequest() {}

    // Getters et setters
    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public List<Offer> getOffers() {
        return offers;
    }

    public void setOffers(List<Offer> offers) {
        this.offers = offers;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCulture() {
        return culture;
    }

    public void setCulture(String culture) {
        this.culture = culture;
    }

    // Classe statique imbriquée pour représenter une offre
    public static class Offer {
        private String offerId;
        private List<String> travellers;

        // Constructeur par défaut
        public Offer() {}

        // Getters et setters
        public String getOfferId() {
            return offerId;
        }

        public void setOfferId(String offerId) {
            this.offerId = offerId;
        }

        public List<String> getTravellers() {
            return travellers;
        }

        public void setTravellers(List<String> travellers) {
            this.travellers = travellers;
        }
    }
}