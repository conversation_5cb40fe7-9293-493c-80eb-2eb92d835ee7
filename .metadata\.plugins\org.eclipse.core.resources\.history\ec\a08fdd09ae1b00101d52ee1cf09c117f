package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.paximum.demo.models.CommitTransactionRequest.PaymentInformation;
import com.paximum.demo.models.CommitTransactionRequest.Price;
import com.paximum.demo.models.CommitTransactionRequest.VCCInformation;
import com.paximum.demo.models.SetReservationInfoRequest.AcademicTitle;
import com.paximum.demo.models.SetReservationInfoRequest.Address;
import com.paximum.demo.models.SetReservationInfoRequest.City;
import com.paximum.demo.models.SetReservationInfoRequest.ContactPhone;
import com.paximum.demo.models.SetReservationInfoRequest.Country;
import com.paximum.demo.models.SetReservationInfoRequest.CustomerInfo;
import com.paximum.demo.models.SetReservationInfoRequest.DestinationAddress;
import com.paximum.demo.models.SetReservationInfoRequest.Document;
import com.paximum.demo.models.SetReservationInfoRequest.InsertField;
import com.paximum.demo.models.SetReservationInfoRequest.Nationality;
import com.paximum.demo.models.SetReservationInfoRequest.PassportInfo;
import com.paximum.demo.models.SetReservationInfoRequest.TaxInfo;
import com.paximum.demo.models.SetReservationInfoRequest.Traveller;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionRequest {
   
	 @JsonProperty("offerIds")
	    private List<String> offerIds;
	    
	    @JsonProperty("reservationNumber")
	    private String reservationNumber;
	    
	    @JsonProperty("currency")
	    private String currency;
	    
	    @JsonProperty("culture")
	    private String culture;
	    private String transactionId;
	    private List<Traveller> travellers;
	    private CustomerInfo customerInfo;
	    private String reservationNote;
	    private String agencyReservationNumber;
	
	    private Integer paymentOption;
	    private PaymentInformation paymentInformation;
	    private VCCInformation vccInformation;


	    // Constructeur par défaut pour Jackson
	    public BookingTransactionRequest() {
	    }

	    // Constructeur pour le cas d'une nouvelle transaction avec offerIds
	    public BookingTransactionRequest(List<String> offerIds, String currency, String culture) {
	        this.offerIds = offerIds;
	        this.currency = currency;
	        this.culture = culture;
	    }

	    // Constructeur pour le cas d'une transaction existante (avec reservationNumber)
	    public BookingTransactionRequest(String reservationNumber, String currency, String culture) {
	        this.reservationNumber = reservationNumber;
	        this.currency = currency;
	        this.culture = culture;
	    }
	    public BookingTransactionRequest( String transactionId, List<Traveller> travellers, CustomerInfo customerInfo,String reservationNote,String agencyReservationNumber) {
	        this.transactionId = transactionId;
	        this.travellers = travellers;
	        this.customerInfo = customerInfo;
	        this.transactionId = transactionId;
	        this.reservationNote = reservationNote;
	        this.agencyReservationNumber = agencyReservationNumber;
	    }
	    
	    public BookingTransactionRequest( String transactionId, Integer paymentOption,PaymentInformation paymentInformation,VCCInformation vccInformation) {
	        this.transactionId = transactionId;
	        this.paymentOption = paymentOption;
	        this.paymentInformation = paymentInformation;
	        this.vccInformation = vccInformation;
	     
	    }
	    
	    

	    public List<String> getOfferIds() {
	        return offerIds;
	    }

	    public void setOfferIds(List<String> offerIds) {
	        this.offerIds = offerIds;
	    }

	    public String getReservationNumber() {
	        return reservationNumber;
	    }

	    public void setReservationNumber(String reservationNumber) {
	        this.reservationNumber = reservationNumber;
	    }

	    public String getCurrency() {
	        return currency;
	    }

	    public void setCurrency(String currency) {
	        this.currency = currency;
	    }

	    public String getCulture() {
	        return culture;
	    }

	    public void setCulture(String culture) {
	        this.culture = culture;
	    }
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	 
	    public List<Traveller> getTravellers() { return travellers; }
	    public void setTravellers(List<Traveller> travellers) { this.travellers = travellers; }
	    public CustomerInfo getCustomerInfo() { return customerInfo; }
	    public void setCustomerInfo(CustomerInfo customerInfo) { this.customerInfo = customerInfo; }
	    public String getReservationNote() { return reservationNote; }
	    public void setReservationNote(String reservationNote) { this.reservationNote = reservationNote; }
	    public String getAgencyReservationNumber() { return agencyReservationNumber; }
	    public void setAgencyReservationNumber(String agencyReservationNumber) { this.agencyReservationNumber = agencyReservationNumber; }

	    // Classes imbriquées
	    public static class Traveller {
	        private String travellerId;
	        private int type;
	        private int title;
	        private AcademicTitle academicTitle;
	        private int passengerType;
	        private String name;
	        private String surname;
	        
	        @JsonProperty("isLeader")
	        private boolean isLeader;
	        
	        private String birthDate;
	        private Nationality nationality;
	        private String identityNumber;
	        private PassportInfo passportInfo;
	        private Address address;
	        private DestinationAddress destinationAddress;
	        private int orderNumber;
	        private List<Document> documents;
	        private List<InsertField> insertFields;
	        private int status;
	        private int gender;

	        // Getters and setters
	        public String getTravellerId() { return travellerId; }
	        public void setTravellerId(String travellerId) { this.travellerId = travellerId; }
	        public int getType() { return type; }
	        public void setType(int type) { this.type = type; }
	        public int getTitle() { return title; }
	        public void setTitle(int title) { this.title = title; }
	        public AcademicTitle getAcademicTitle() { return academicTitle; }
	        public void setAcademicTitle(AcademicTitle academicTitle) { this.academicTitle = academicTitle; }
	        public int getPassengerType() { return passengerType; }
	        public void setPassengerType(int passengerType) { this.passengerType = passengerType; }
	        public String getName() { return name; }
	        public void setName(String name) { this.name = name; }
	        public String getSurname() { return surname; }
	        public void setSurname(String surname) { this.surname = surname; }
	        
	        @JsonProperty("isLeader")
	        public boolean isLeader() { return isLeader; }
	        
	        @JsonProperty("isLeader")
	        public void setLeader(boolean isLeader) { this.isLeader = isLeader; }
	        
	        public String getBirthDate() { return birthDate; }
	        public void setBirthDate(String birthDate) { this.birthDate = birthDate; }
	        public Nationality getNationality() { return nationality; }
	        public void setNationality(Nationality nationality) { this.nationality = nationality; }
	        public String getIdentityNumber() { return identityNumber; }
	        public void setIdentityNumber(String identityNumber) { this.identityNumber = identityNumber; }
	        public PassportInfo getPassportInfo() { return passportInfo; }
	        public void setPassportInfo(PassportInfo passportInfo) { this.passportInfo = passportInfo; }
	        public Address getAddress() { return address; }
	        public void setAddress(Address address) { this.address = address; }
	        public DestinationAddress getDestinationAddress() { return destinationAddress; }
	        public void setDestinationAddress(DestinationAddress destinationAddress) { this.destinationAddress = destinationAddress; }
	        public int getOrderNumber() { return orderNumber; }
	        public void setOrderNumber(int orderNumber) { this.orderNumber = orderNumber; }
	        public List<Document> getDocuments() { return documents; }
	        public void setDocuments(List<Document> documents) { this.documents = documents; }
	        public List<InsertField> getInsertFields() { return insertFields; }
	        public void setInsertFields(List<InsertField> insertFields) { this.insertFields = insertFields; }
	        public int getStatus() { return status; }
	        public void setStatus(int status) { this.status = status; }
	        public int getGender() { return gender; }
	        public void setGender(int gender) { this.gender = gender; }
	    }

	    public static class AcademicTitle {
	        private int id;

	        public int getId() { return id; }
	        public void setId(int id) { this.id = id; }
	    }

	    public static class Nationality {
	        private String twoLetterCode;

	        public String getTwoLetterCode() { return twoLetterCode; }
	        public void setTwoLetterCode(String twoLetterCode) { this.twoLetterCode = twoLetterCode; }
	    }

	    public static class PassportInfo {
	        private String serial;
	        private String number;
	        private String expireDate;
	        private String issueDate;
	        private String citizenshipCountryCode;
	        private String issueCountryCode; // Champ ajouté, obligatoire pour l'API Paximum

	        public String getSerial() { return serial; }
	        public void setSerial(String serial) { this.serial = serial; }
	        public String getNumber() { return number; }
	        public void setNumber(String number) { this.number = number; }
	        public String getExpireDate() { return expireDate; }
	        public void setExpireDate(String expireDate) { this.expireDate = expireDate; }
	        public String getIssueDate() { return issueDate; }
	        public void setIssueDate(String issueDate) { this.issueDate = issueDate; }
	        public String getCitizenshipCountryCode() { return citizenshipCountryCode; }
	        public void setCitizenshipCountryCode(String citizenshipCountryCode) { this.citizenshipCountryCode = citizenshipCountryCode; }
	        public String getIssueCountryCode() { return issueCountryCode; }
	        public void setIssueCountryCode(String issueCountryCode) { this.issueCountryCode = issueCountryCode; }
	    }

	    public static class Address {
	        private ContactPhone contactPhone;
	        private String email;
	        private String address;
	        private String zipCode;
	        private City city;
	        private Country country;

	        public ContactPhone getContactPhone() { return contactPhone; }
	        public void setContactPhone(ContactPhone contactPhone) { this.contactPhone = contactPhone; }
	        public String getEmail() { return email; }
	        public void setEmail(String email) { this.email = email; }
	        public String getAddress() { return address; }
	        public void setAddress(String address) { this.address = address; }
	        public String getZipCode() { return zipCode; }
	        public void setZipCode(String zipCode) { this.zipCode = zipCode; }
	        public City getCity() { return city; }
	        public void setCity(City city) { this.city = city; }
	        public Country getCountry() { return country; }
	        public void setCountry(Country country) { this.country = country; }
	    }

	    public static class ContactPhone {
	        private String countryCode;
	        private String areaCode;
	        private String phoneNumber;

	        public String getCountryCode() { return countryCode; }
	        public void setCountryCode(String countryCode) { this.countryCode = countryCode; }
	        public String getAreaCode() { return areaCode; }
	        public void setAreaCode(String areaCode) { this.areaCode = areaCode; }
	        public String getPhoneNumber() { return phoneNumber; }
	        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
	    }

	    public static class City {
	        private String id;
	        private String name;

	        public String getId() { return id; }
	        public void setId(String id) { this.id = id; }
	        public String getName() { return name; }
	        public void setName(String name) { this.name = name; }
	    }

	    public static class Country {
	        private String id;
	        private String name;

	        public String getId() { return id; }
	        public void setId(String id) { this.id = id; }
	        public String getName() { return name; }
	        public void setName(String name) { this.name = name; }
	    }

	    public static class DestinationAddress {
	        // Peut être étendu si nécessaire
	    }

	    public static class Document {
	        // Peut être étendu si nécessaire
	    }

	    public static class InsertField {
	        // Peut être étendu si nécessaire
	    }

	    public static class CustomerInfo {
	        private boolean isCompany;
	        private PassportInfo passportInfo;
	        private Address address;
	        private TaxInfo taxInfo;
	        private int title;
	        private String name;
	        private String surname;
	        private String birthDate;
	        private String identityNumber;

	        public boolean isCompany() { return isCompany; }
	        public void setCompany(boolean isCompany) { this.isCompany = isCompany; }
	        public PassportInfo getPassportInfo() { return passportInfo; }
	        public void setPassportInfo(PassportInfo passportInfo) { this.passportInfo = passportInfo; }
	        public Address getAddress() { return address; }
	        public void setAddress(Address address) { this.address = address; }
	        public TaxInfo getTaxInfo() { return taxInfo; }
	        public void setTaxInfo(TaxInfo taxInfo) { this.taxInfo = taxInfo; }
	        public int getTitle() { return title; }
	        public void setTitle(int title) { this.title = title; }
	        public String getName() { return name; }
	        public void setName(String name) { this.name = name; }
	        public String getSurname() { return surname; }
	        public void setSurname(String surname) { this.surname = surname; }
	        public String getBirthDate() { return birthDate; }
	        public void setBirthDate(String birthDate) { this.birthDate = birthDate; }
	        public String getIdentityNumber() { return identityNumber; }
	        public void setIdentityNumber(String identityNumber) { this.identityNumber = identityNumber; }
	    }

	    public static class TaxInfo {
	        private String taxOffice;
	        private String taxNumber;

	        public String getTaxOffice() { return taxOffice; }
	        public void setTaxOffice(String taxOffice) { this.taxOffice = taxOffice; }
	        public String getTaxNumber() { return taxNumber; }
	        public void setTaxNumber(String taxNumber) { this.taxNumber = taxNumber; }
	    }
	    
	    
	    
	    
	    
	    

	    public static class PaymentInformation {
	        private String accountName;
	        private Integer paymentTypeId;
	        private Price paymentPrice;
	        private String installmentCount;
	        private String paymentDate;
	        private String receiptType;
	        private String reference;
	        private String paymentToken;

	        public PaymentInformation() {}

	        public String getAccountName() { return accountName; }
	        public void setAccountName(String accountName) { this.accountName = accountName; }
	        public Integer getPaymentTypeId() { return paymentTypeId; }
	        public void setPaymentTypeId(Integer paymentTypeId) { this.paymentTypeId = paymentTypeId; }
	        public Price getPaymentPrice() { return paymentPrice; }
	        public void setPaymentPrice(Price paymentPrice) { this.paymentPrice = paymentPrice; }
	        public String getInstallmentCount() { return installmentCount; }
	        public void setInstallmentCount(String installmentCount) { this.installmentCount = installmentCount; }
	        public String getPaymentDate() { return paymentDate; }
	        public void setPaymentDate(String paymentDate) { this.paymentDate = paymentDate; }
	        public String getReceiptType() { return receiptType; }
	        public void setReceiptType(String receiptType) { this.receiptType = receiptType; }
	        public String getReference() { return reference; }
	        public void setReference(String reference) { this.reference = reference; }
	        public String getPaymentToken() { return paymentToken; }
	        public void setPaymentToken(String paymentToken) { this.paymentToken = paymentToken; }
	    }

	    public static class VCCInformation {
	        private String vccNo;
	        private String vccExpDate;
	        private String vccSecNo;
	        private String vccHolderName;

	        public VCCInformation() {}

	        public String getVccNo() { return vccNo; }
	        public void setVccNo(String vccNo) { this.vccNo = vccNo; }
	        public String getVccExpDate() { return vccExpDate; }
	        public void setVccExpDate(String vccExpDate) { this.vccExpDate = vccExpDate; }
	        public String getVccSecNo() { return vccSecNo; }
	        public void setVccSecNo(String vccSecNo) { this.vccSecNo = vccSecNo; }
	        public String getVccHolderName() { return vccHolderName; }
	        public void setVccHolderName(String vccHolderName) { this.vccHolderName = vccHolderName; }
	    }

	    public static class Price {
	        private Double amount;
	        private String currency;

	        public Price() {}

	        public Double getAmount() { return amount; }
	        public void setAmount(Double amount) { this.amount = amount; }
	        public String getCurrency() { return currency; }
	        public void setCurrency(String currency) { this.currency = currency; }
	    }
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
	    
}