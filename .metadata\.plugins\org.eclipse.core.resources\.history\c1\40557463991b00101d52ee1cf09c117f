package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
    private String action;
    private Object data;
    private boolean success;
    private String message;

    // Constructor for begin transaction response
    public BookingTransactionResponse(String action, Object data, boolean success, String message) {
        this.action = action;
        this.data = data;
        this.success = success;
        this.message = message;
    }

    // Default constructor for Jackson
    public BookingTransactionResponse() {
    }

    // Getters and Setters
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    // Helper methods to retrieve specific response types
    public BeginTransactionResponse getBeginResponse() {
        if (data instanceof BeginTransactionResponse) {
            return (BeginTransactionResponse) data;
        }
        return null;
    }

    public SetReservationInfoResponse getInfoResponse() {
        if (data instanceof SetReservationInfoResponse) {
            return (SetReservationInfoResponse) data;
        }
        return null;
    }

    public CommitTransactionResponse getCommitResponse() {
        if (data instanceof CommitTransactionResponse) {
            return (CommitTransactionResponse) data;
        }
        return null;
    }
}