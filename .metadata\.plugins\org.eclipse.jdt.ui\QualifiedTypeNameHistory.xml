<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<qualifiedTypeNameHistroy>
    <fullyQualifiedTypeName name="com.paximum.auth.models.ApiResponse"/>
    <fullyQualifiedTypeName name="com.paximum.auth.models.AuthRequest"/>
    <fullyQualifiedTypeName name="com.paximum.productservice.services.PriceMethodeService"/>
    <fullyQualifiedTypeName name="com.paximum.demo.models.CommitTransactionResponse.Header"/>
    <fullyQualifiedTypeName name="com.paximum.demo.models.BeginTransactionResponse.Body"/>
    <fullyQualifiedTypeName name="com.paximum.demo.models.SetReservationInfoResponse.Body"/>
    <fullyQualifiedTypeName name="com.paximum.demo.models.CommitTransactionResponse.Body"/>
    <fullyQualifiedTypeName name="com.paximum.demo.models.BeginTransactionResponse.Header"/>
    <fullyQualifiedTypeName name="com.paximum.demo.models.SetReservationInfoResponse.Header"/>
</qualifiedTypeNameHistroy>
