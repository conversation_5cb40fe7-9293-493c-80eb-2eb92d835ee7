package com.paximum.demo.models;

import java.util.List;

public class Header {
    private String requestId;
    private boolean success;
    private String responseTime;
    private List<Message> messages;
    
    public Header() {}
    
    public Header(String requestId, boolean success, String responseTime, List<Message> messages) {
        this.requestId = requestId;
        this.success = success;
        this.responseTime = responseTime;
        this.messages = messages;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getResponseTime() {
        return responseTime;
    }
    
    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }
    
    public List<Message> getMessages() {
        return messages;
    }
    
    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }
}
