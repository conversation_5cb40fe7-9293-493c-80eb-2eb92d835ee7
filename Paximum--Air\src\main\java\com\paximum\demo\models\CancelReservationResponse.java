package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CancelReservationResponse {
    private Header header;
    private Body body;

    public CancelReservationResponse() {}

    public Header getHeader() { return header; }
    public void setHeader(Header header) { this.header = header; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }

    public static class Body {
        private Integer reservationStatus;

        public Integer getReservationStatus() { return reservationStatus; }
        public void setReservationStatus(Integer reservationStatus) { this.reservationStatus = reservationStatus; }
    }
}
