<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename type &apos;com.paximum.auth.models.ApiResponseHeader&apos; to &apos;Header&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.models.ApiResponseHeader&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.auth.models.Header&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;ApiResponseHeader&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models{ApiResponseHeader.java[ApiResponseHeader" matchStrategy="1" name="Header" qualified="false" references="true" similarDeclarations="false" stamp="1741774994325" textual="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.models.Header.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models{Header.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741775001939" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.models.ApiResponseBody.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models{ApiResponseBody.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741775006037" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.productservice.models&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.models" element2="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice" elements="2" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741776264041" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.auth&apos; to &apos;com.paximum.demo&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.auth&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.demo&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.auth&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth" name="com.paximum.demo" qualified="false" references="true" stamp="1741776859803" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.auth.config&apos; to &apos;com.paximum.demo.config&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.demo.config&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.auth.config&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config" name="com.paximum.demo.config" qualified="false" references="true" stamp="1741776893136" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.auth.models&apos; to &apos;com.paximum.demo.models&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.auth.models&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.demo.models&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.auth.models&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models" name="com.paximum.demo.models" qualified="false" references="true" stamp="1741776924122" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.auth.services&apos; to &apos;com.paximum.demo.services&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.auth.services&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.demo.services&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.auth.services&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.services" name="com.paximum.demo.services" qualified="false" references="true" stamp="1741776960646" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.auth.controllers&apos; to &apos;com.paximum.demo.controllers&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.demo.controllers&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.auth.controllers&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.controllers" name="com.paximum.demo.controllers" qualified="false" references="true" stamp="1741776971504" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.BodyPService&apos; to &apos;Body_PService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.BodyPService&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.Body_PService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;BodyPService&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{BodyPService.java[BodyPService" matchStrategy="1" name="Body_PService" qualified="false" references="true" similarDeclarations="false" stamp="1741958925259" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.FlightProviderPservice&apos; to &apos;FlightProvider_Pservice&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.FlightProviderPservice&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.FlightProvider_Pservice&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;FlightProviderPservice&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightProviderPservice.java[FlightProviderPservice" matchStrategy="1" name="FlightProvider_Pservice" qualified="false" references="true" similarDeclarations="false" stamp="1741958949723" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.FlightPService&apos; to &apos;Flight_PService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.FlightPService&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.Flight_PService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;FlightPService&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightPService.java[FlightPService" matchStrategy="1" name="Flight_PService" qualified="false" references="true" similarDeclarations="false" stamp="1741958960890" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.HeaderPService&apos; to &apos;Header_PService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.HeaderPService&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.Header_PService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;HeaderPService&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{HeaderPService.java[HeaderPService" matchStrategy="1" name="Header_PService" qualified="false" references="true" similarDeclarations="false" stamp="1741958973016" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.ItemPService&apos; to &apos;Item_PService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.ItemPService&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.Item_PService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;ItemPService&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{ItemPService.java[ItemPService" matchStrategy="1" name="Item_PService" qualified="false" references="true" similarDeclarations="false" stamp="1741958982794" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.MessagePService&apos; to &apos;Message_PService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.MessagePService&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.Message_PService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;MessagePService&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{MessagePService.java[MessagePService" matchStrategy="1" name="Message_PService" qualified="false" references="true" similarDeclarations="false" stamp="1741958998225" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename compilation unit &apos;com.paximum.demo.models.FlightProvider_Pservice.java&apos; to &apos;FlightProvider_PService.java&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.FlightProvider_Pservice.java&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.FlightProvider_PService.java&apos;" description="Rename compilation unit &apos;FlightProvider_Pservice.java&apos;" flags="2" id="org.eclipse.jdt.ui.rename.compilationunit" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightProvider_Pservice.java" name="FlightProvider_PService.java" references="false" stamp="1741961037535" version="1.0"/>
</session>