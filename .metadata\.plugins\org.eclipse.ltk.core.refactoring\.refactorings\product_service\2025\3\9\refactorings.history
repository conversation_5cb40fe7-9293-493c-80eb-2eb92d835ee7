<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Flight_Product&apos;&#x0D;&#x0A;- Original project: &apos;Flight_Product&apos;&#x0D;&#x0A;- Original element: &apos;com.example.FP.services.searchprice.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP.services{searchprice.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740840107655" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move element &apos;priceMethodeService.java&apos; to &apos;Flight_Product/src/main/java/com.example.FP.services&apos;&#x0D;&#x0A;- Original project: &apos;Flight_Product&apos;&#x0D;&#x0A;- Destination element: &apos;Flight_Product/src/main/java/com.example.FP.services&apos;&#x0D;&#x0A;- Original element: &apos;com.example.FP.controllers.priceMethodeService.java&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move compilation unit" destination="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP.services" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP.controllers{priceMethodeService.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.move" patterns="*" policy="org.eclipse.jdt.ui.moveResources" qualified="false" references="true" stamp="1740840194117" units="1" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.example.FP&apos; to &apos;com.example.prod&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Original element: &apos;product_service/src/main/java/com.example.FP&apos;&#x0D;&#x0A;- Renamed element: &apos;product_service/src/main/java/com.example.prod&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.example.FP&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP" name="com.example.prod" qualified="false" references="true" stamp="1740844595147" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.example.FP.services&apos; to &apos;com.example.prod.services&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Original element: &apos;product_service/src/main/java/com.example.FP.services&apos;&#x0D;&#x0A;- Renamed element: &apos;product_service/src/main/java/com.example.prod.services&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.example.FP.services&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP.services" name="com.example.prod.services" qualified="false" references="true" stamp="1740844608954" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.example.FP.controllers&apos; to &apos;com.example.prod.controllers&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Original element: &apos;product_service/src/main/java/com.example.FP.controllers&apos;&#x0D;&#x0A;- Renamed element: &apos;product_service/src/main/java/com.example.prod.controllers&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.example.FP.controllers&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP.controllers" name="com.example.prod.controllers" qualified="false" references="true" stamp="1740844637137" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.example.FP.config&apos; to &apos;com.example.prod.config&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Original element: &apos;product_service/src/main/java/com.example.FP.config&apos;&#x0D;&#x0A;- Renamed element: &apos;product_service/src/main/java/com.example.prod.config&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.example.FP.config&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.FP.config" name="com.example.prod.config" qualified="false" references="true" stamp="1740844647675" textual="false" version="1.0"/>
</session>