package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paximum.demo.models.BeginTransactionResponse.Body;
import com.paximum.demo.models.BeginTransactionResponse.Header;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
	 private BeginTransactionResponse.Header header1;

	 
	 private BeginTransactionResponse.Body body1;



 
	    public Header getHeader() {
	        return header1;
	    }

	    public void setHeader(Header header) {
	        this.header1 = header;
	    }

	    public Body getBody() {
	        return body1;
	    }

	    public void setBody(Body body) {
	        this.body1 = body;
	    }


   

}