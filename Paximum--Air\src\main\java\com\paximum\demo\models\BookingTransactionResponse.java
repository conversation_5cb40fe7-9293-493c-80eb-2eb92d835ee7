package com.paximum.demo.models;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Classe qui encapsule les différents types de réponses de transaction de réservation.
 * Utilisée pour l'endpoint /booking-transaction avec paramètre action.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {

    @JsonProperty("action")
    private String action;

    @JsonProperty("beginResponse")
    private BeginTransactionResponse beginResponse;

    @JsonProperty("infoResponse")
    private SetReservationInfoResponse infoResponse;

    @JsonProperty("commitResponse")
    private CommitTransactionResponse commitResponse;

    // Constructeur par défaut pour Jackson
    public BookingTransactionResponse() {
    }

    // Constructeur pour réponse de début de transaction
    public BookingTransactionResponse(BeginTransactionResponse beginResponse) {
        this.action = "begin";
        this.beginResponse = beginResponse;
    }

    // Constructeur pour réponse de définition des informations de réservation
    public BookingTransactionResponse(SetReservationInfoResponse infoResponse) {
        this.action = "info";
        this.infoResponse = infoResponse;
    }

    // Constructeur pour réponse de finalisation de transaction
    public BookingTransactionResponse(CommitTransactionResponse commitResponse) {
        this.action = "commit";
        this.commitResponse = commitResponse;
    }

    // Getters et Setters
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public BeginTransactionResponse getBeginResponse() {
        return beginResponse;
    }

    public void setBeginResponse(BeginTransactionResponse beginResponse) {
        this.beginResponse = beginResponse;
    }

    public SetReservationInfoResponse getInfoResponse() {
        return infoResponse;
    }

    public void setInfoResponse(SetReservationInfoResponse infoResponse) {
        this.infoResponse = infoResponse;
    }

    public CommitTransactionResponse getCommitResponse() {
        return commitResponse;
    }

    public void setCommitResponse(CommitTransactionResponse commitResponse) {
        this.commitResponse = commitResponse;
    }
}
