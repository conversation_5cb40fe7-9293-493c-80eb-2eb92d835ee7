package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paximum.demo.models.BeginTransactionResponse.Body;
import com.paximum.demo.models.BeginTransactionResponse.Header;
import com.paximum.demo.models.BeginTransactionResponse.Message;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
	 private Header header;
	  private Body body;
    private String action;
    private Object data;
    private boolean success;
    private String message;
    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }
    // Classe Header
    public static class Header {
        private String requestId;
        private boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(String responseTime) {
            this.responseTime = responseTime;
        }

        public List<Message> getMessages() {
            return messages;
        }

        public void setMessages(List<Message> messages) {
            this.messages = messages;
        }
    }
    // Classe Message
    public static class Message {
        private int id;
        private String code;
        private int messageType;
        private String message;

        public Message() {
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getMessageType() {
            return messageType;
        }

        public void setMessageType(int messageType) {
            this.messageType = messageType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    // Constructor for begin transaction response
    public BookingTransactionResponse(String action, Object data, boolean success, String message) {
        this.action = action;
        this.data = data;
        this.success = success;
        this.message = message;
    }

    // Default constructor for Jackson
    public BookingTransactionResponse() {
    }

    // Getters and Setters
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    // Helper methods to retrieve specific response types
    public BeginTransactionResponse getBeginResponse() {
        if (data instanceof BeginTransactionResponse) {
            return (BeginTransactionResponse) data;
        }
        return null;
    }

    public SetReservationInfoResponse getInfoResponse() {
        if (data instanceof SetReservationInfoResponse) {
            return (SetReservationInfoResponse) data;
        }
        return null;
    }

    public CommitTransactionResponse getCommitResponse() {
        if (data instanceof CommitTransactionResponse.Header) {
            return (CommitTransactionResponse) data;
        }
        return null;
    }
}