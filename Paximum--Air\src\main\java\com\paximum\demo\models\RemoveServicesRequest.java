package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RemoveServicesRequest {
    private String transactionId;
    private List<String> services;
    private String currency;
    private String culture;

    public RemoveServicesRequest() {}

    public RemoveServicesRequest(String transactionId, List<String> services, String currency, String culture) {
        this.transactionId = transactionId;
        this.services = services;
        this.currency = currency;
        this.culture = culture;
    }

    public String getTransactionId() { return transactionId; }
    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
    public List<String> getServices() { return services; }
    public void setServices(List<String> services) { this.services = services; }
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
    public String getCulture() { return culture; }
    public void setCulture(String culture) { this.culture = culture; }
}