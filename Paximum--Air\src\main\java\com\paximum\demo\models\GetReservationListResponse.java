package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetReservationListResponse {

    private Header header;
    private Body body;

    // Constructors
    public GetReservationListResponse() {}

    // Getters and Setters
    public Header getHeader() { return header; }
    public void setHeader(Header header) { this.header = header; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }

    // Nested static class for Header (reused from GetReservationDetailResponse)
    public static class Header {
        private String requestId;
        private Boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {}

        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        public Boolean getSuccess() { return success; }
        public void setSuccess(Boolean success) { this.success = success; }
        public String getResponseTime() { return responseTime; }
        public void setResponseTime(String responseTime) { this.responseTime = responseTime; }
        public List<Message> getMessages() { return messages; }
        public void setMessages(List<Message> messages) { this.messages = messages; }
    }

    // Nested static class for Message (reused from GetReservationDetailResponse)
    public static class Message {
        private Integer id;
        private String code;
        private Integer messageType;
        private String message;

        public Message() {}

        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getMessageType() { return messageType; }
        public void setMessageType(Integer messageType) { this.messageType = messageType; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    // Nested static class for Body
    public static class Body {
        private List<Reservation> reservations;
        private Integer totalCount;
        private Integer maxIndexNumber;
        private Integer minIndexNumber;

        public Body() {}

        public List<Reservation> getReservations() { return reservations; }
        public void setReservations(List<Reservation> reservations) { this.reservations = reservations; }
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public Integer getMaxIndexNumber() { return maxIndexNumber; }
        public void setMaxIndexNumber(Integer maxIndexNumber) { this.maxIndexNumber = maxIndexNumber; }
        public Integer getMinIndexNumber() { return minIndexNumber; }
        public void setMinIndexNumber(Integer minIndexNumber) { this.minIndexNumber = minIndexNumber; }
    }

    // Nested static class for Reservation
    public static class Reservation {
        private String reservationNumber;
        private String encryptedReservationNumber;
        private String beginDate;
        private String endDate;
        private Integer night;
        private String leaderName;
        private String agency;
        private String agencyUser;
        private Integer adult;
        private Integer child;
        private String registerDate;
        private Integer reservationStatus;
        private Integer confirmationStatus;
        private Integer paymentStatus;
        private Price salePrice;
        private City departureCity;
        private City arrivalCity;
        private String reservationNote;
        private Boolean readByOperator;
        private String readByOperatorUser;
        private String serviceTypes;
        private Boolean newComment;
        private Double agencyPayment;
        private Double discount;
        private Double agencyCommission;
        private Double agencyDiscountCommission;
        private Double agencyEarlyBooking;
        private Double agencyAmountToPay;
        private Double passengerAmountToPay;
        private Double passengerEarlyBooking;
        private Double passengerBonus;
        private Double usedPassengerBonus;
        private Double agencyBonus;
        private Double usedAgencyBonus;
        private Double userBonus;
        private Double usedUserBonus;
        private List<PaymentPlan> paymentPlan;
        private List<Document> documents;
        private String agencyReservationNumber;
        private String ticketNos;
        private List<Service> services;
        private Boolean hasMessageHistory;
        private Integer invoiceStatus;
        private Integer rowNumber;
        private Boolean ownSupplier;
        private String createDate;
        private String confirmationDate;
        private String pnrNo;

        public Reservation() {}

        public String getReservationNumber() { return reservationNumber; }
        public void setReservationNumber(String reservationNumber) { this.reservationNumber = reservationNumber; }
        public String getEncryptedReservationNumber() { return encryptedReservationNumber; }
        public void setEncryptedReservationNumber(String encryptedReservationNumber) { this.encryptedReservationNumber = encryptedReservationNumber; }
        public String getBeginDate() { return beginDate; }
        public void setBeginDate(String beginDate) { this.beginDate = beginDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public Integer getNight() { return night; }
        public void setNight(Integer night) { this.night = night; }
        public String getLeaderName() { return leaderName; }
        public void setLeaderName(String leaderName) { this.leaderName = leaderName; }
        public String getAgency() { return agency; }
        public void setAgency(String agency) { this.agency = agency; }
        public String getAgencyUser() { return agencyUser; }
        public void setAgencyUser(String agencyUser) { this.agencyUser = agencyUser; }
        public Integer getAdult() { return adult; }
        public void setAdult(Integer adult) { this.adult = adult; }
        public Integer getChild() { return child; }
        public void setChild(Integer child) { this.child = child; }
        public String getRegisterDate() { return registerDate; }
        public void setRegisterDate(String registerDate) { this.registerDate = registerDate; }
        public Integer getReservationStatus() { return reservationStatus; }
        public void setReservationStatus(Integer reservationStatus) { this.reservationStatus = reservationStatus; }
        public Integer getConfirmationStatus() { return confirmationStatus; }
        public void setConfirmationStatus(Integer confirmationStatus) { this.confirmationStatus = confirmationStatus; }
        public Integer getPaymentStatus() { return paymentStatus; }
        public void setPaymentStatus(Integer paymentStatus) { this.paymentStatus = paymentStatus; }
        public Price getSalePrice() { return salePrice; }
        public void setSalePrice(Price salePrice) { this.salePrice = salePrice; }
        public City getDepartureCity() { return departureCity; }
        public void setDepartureCity(City departureCity) { this.departureCity = departureCity; }
        public City getArrivalCity() { return arrivalCity; }
        public void setArrivalCity(City arrivalCity) { this.arrivalCity = arrivalCity; }
        public String getReservationNote() { return reservationNote; }
        public void setReservationNote(String reservationNote) { this.reservationNote = reservationNote; }
        public Boolean getReadByOperator() { return readByOperator; }
        public void setReadByOperator(Boolean readByOperator) { this.readByOperator = readByOperator; }
        public String getReadByOperatorUser() { return readByOperatorUser; }
        public void setReadByOperatorUser(String readByOperatorUser) { this.readByOperatorUser = readByOperatorUser; }
        public String getServiceTypes() { return serviceTypes; }
        public void setServiceTypes(String serviceTypes) { this.serviceTypes = serviceTypes; }
        public Boolean getNewComment() { return newComment; }
        public void setNewComment(Boolean newComment) { this.newComment = newComment; }
        public Double getAgencyPayment() { return agencyPayment; }
        public void setAgencyPayment(Double agencyPayment) { this.agencyPayment = agencyPayment; }
        public Double getDiscount() { return discount; }
        public void setDiscount(Double discount) { this.discount = discount; }
        public Double getAgencyCommission() { return agencyCommission; }
        public void setAgencyCommission(Double agencyCommission) { this.agencyCommission = agencyCommission; }
        public Double getAgencyDiscountCommission() { return agencyDiscountCommission; }
        public void setAgencyDiscountCommission(Double agencyDiscountCommission) { this.agencyDiscountCommission = agencyDiscountCommission; }
        public Double getAgencyEarlyBooking() { return agencyEarlyBooking; }
        public void setAgencyEarlyBooking(Double agencyEarlyBooking) { this.agencyEarlyBooking = agencyEarlyBooking; }
        public Double getAgencyAmountToPay() { return agencyAmountToPay; }
        public void setAgencyAmountToPay(Double agencyAmountToPay) { this.agencyAmountToPay = agencyAmountToPay; }
        public Double getPassengerAmountToPay() { return passengerAmountToPay; }
        public void setPassengerAmountToPay(Double passengerAmountToPay) { this.passengerAmountToPay = passengerAmountToPay; }
        public Double getPassengerEarlyBooking() { return passengerEarlyBooking; }
        public void setPassengerEarlyBooking(Double passengerEarlyBooking) { this.passengerEarlyBooking = passengerEarlyBooking; }
        public Double getPassengerBonus() { return passengerBonus; }
        public void setPassengerBonus(Double passengerBonus) { this.passengerBonus = passengerBonus; }
        public Double getUsedPassengerBonus() { return usedPassengerBonus; }
        public void setUsedPassengerBonus(Double usedPassengerBonus) { this.usedPassengerBonus = usedPassengerBonus; }
        public Double getAgencyBonus() { return agencyBonus; }
        public void setAgencyBonus(Double agencyBonus) { this.agencyBonus = agencyBonus; }
        public Double getUsedAgencyBonus() { return usedAgencyBonus; }
        public void setUsedAgencyBonus(Double usedAgencyBonus) { this.usedAgencyBonus = usedAgencyBonus; }
        public Double getUserBonus() { return userBonus; }
        public void setUserBonus(Double userBonus) { this.userBonus = userBonus; }
        public Double getUsedUserBonus() { return usedUserBonus; }
        public void setUsedUserBonus(Double usedUserBonus) { this.usedUserBonus = usedUserBonus; }
        public List<PaymentPlan> getPaymentPlan() { return paymentPlan; }
        public void setPaymentPlan(List<PaymentPlan> paymentPlan) { this.paymentPlan = paymentPlan; }
        public List<Document> getDocuments() { return documents; }
        public void setDocuments(List<Document> documents) { this.documents = documents; }
        public String getAgencyReservationNumber() { return agencyReservationNumber; }
        public void setAgencyReservationNumber(String agencyReservationNumber) { this.agencyReservationNumber = agencyReservationNumber; }
        public String getTicketNos() { return ticketNos; }
        public void setTicketNos(String ticketNos) { this.ticketNos = ticketNos; }
        public List<Service> getServices() { return services; }
        public void setServices(List<Service> services) { this.services = services; }
        public Boolean getHasMessageHistory() { return hasMessageHistory; }
        public void setHasMessageHistory(Boolean hasMessageHistory) { this.hasMessageHistory = hasMessageHistory; }
        public Integer getInvoiceStatus() { return invoiceStatus; }
        public void setInvoiceStatus(Integer invoiceStatus) { this.invoiceStatus = invoiceStatus; }
        public Integer getRowNumber() { return rowNumber; }
        public void setRowNumber(Integer rowNumber) { this.rowNumber = rowNumber; }
        public Boolean getOwnSupplier() { return ownSupplier; }
        public void setOwnSupplier(Boolean ownSupplier) { this.ownSupplier = ownSupplier; }
        public String getCreateDate() { return createDate; }
        public void setCreateDate(String createDate) { this.createDate = createDate; }
        public String getConfirmationDate() { return confirmationDate; }
        public void setConfirmationDate(String confirmationDate) { this.confirmationDate = confirmationDate; }
        public String getPnrNo() { return pnrNo; }
        public void setPnrNo(String pnrNo) { this.pnrNo = pnrNo; }
    }

    // Nested static class for Price (reused from GetReservationDetailResponse)
    public static class Price {
        private Double amount;
        private String currency;

        public Price() {}

        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }

    // Nested static class for City (reused from GetReservationDetailResponse)
    public static class City {
        private String name;
        private Integer provider;
        private Boolean isTopRegion;
        private String id;

        public City() {}

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public Boolean getIsTopRegion() { return isTopRegion; }
        public void setIsTopRegion(Boolean isTopRegion) { this.isTopRegion = isTopRegion; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
    }

    // Nested static class for PaymentPlan
    public static class PaymentPlan {
        private Integer paymentNo;
        private String dueDate;
        private PriceWithPercent price;
        private Boolean paymentStatus;

        public PaymentPlan() {}

        public Integer getPaymentNo() { return paymentNo; }
        public void setPaymentNo(Integer paymentNo) { this.paymentNo = paymentNo; }
        public String getDueDate() { return dueDate; }
        public void setDueDate(String dueDate) { this.dueDate = dueDate; }
        public PriceWithPercent getPrice() { return price; }
        public void setPrice(PriceWithPercent price) { this.price = price; }
        public Boolean getPaymentStatus() { return paymentStatus; }
        public void setPaymentStatus(Boolean paymentStatus) { this.paymentStatus = paymentStatus; }
    }

    // Nested static class for PriceWithPercent (reused from GetReservationDetailResponse)
    public static class PriceWithPercent {
        private Integer percent;
        private Double amount;
        private String currency;

        public PriceWithPercent() {}

        public Integer getPercent() { return percent; }
        public void setPercent(Integer percent) { this.percent = percent; }
        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }

    // Nested static class for Document
    public static class Document {
        private Integer documentType;
        private String url;
        private Boolean isDefault;
        private Boolean proforma;

        public Document() {}

        public Integer getDocumentType() { return documentType; }
        public void setDocumentType(Integer documentType) { this.documentType = documentType; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public Boolean getIsDefault() { return isDefault; }
        public void setIsDefault(Boolean isDefault) { this.isDefault = isDefault; }
        public Boolean getProforma() { return proforma; }
        public void setProforma(Boolean proforma) { this.proforma = proforma; }
    }

    // Nested static class for Service
    public static class Service {
        private String code;
        private String name;
        private Integer productType;
        private String serviceType;
        private String sendSupplier;
        private Integer statConf;

        public Service() {}

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getProductType() { return productType; }
        public void setProductType(Integer productType) { this.productType = productType; }
        public String getServiceType() { return serviceType; }
        public void setServiceType(String serviceType) { this.serviceType = serviceType; }
        public String getSendSupplier() { return sendSupplier; }
        public void setSendSupplier(String sendSupplier) { this.sendSupplier = sendSupplier; }
        public Integer getStatConf() { return statConf; }
        public void setStatConf(Integer statConf) { this.statConf = statConf; }
    }
}