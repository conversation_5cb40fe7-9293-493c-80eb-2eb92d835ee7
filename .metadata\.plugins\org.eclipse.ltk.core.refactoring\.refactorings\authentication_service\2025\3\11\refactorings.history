<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Copy element &apos;AuthClient.java&apos; to &apos;Paximum--Air/src/main/java/config&apos;&#x0D;&#x0A;- Original project: &apos;authentication_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/config&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.config.AuthClient.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;config" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config{AuthClient.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741771888208" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;AuthClient.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.config&apos;&#x0D;&#x0A;- Original project: &apos;authentication_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.config&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.config.AuthClient.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.config" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config{AuthClient.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741771967433" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;AuthController.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.controllers&apos;&#x0D;&#x0A;- Original project: &apos;authentication_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.controllers&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.controllers.AuthController.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.controllers" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.controllers{AuthController.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741771973401" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;ApiResponse.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.models&apos;&#x0D;&#x0A;- Original project: &apos;authentication_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.models&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.models.ApiResponse.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.models" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models{ApiResponse.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741771978087" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;AuthRequest.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.models&apos;&#x0D;&#x0A;- Original project: &apos;authentication_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.models&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.models.AuthRequest.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.models" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models{AuthRequest.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741771982389" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;AuthService.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.services&apos;&#x0D;&#x0A;- Original project: &apos;authentication_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.services&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.services.AuthService.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.services" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.services{AuthService.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741771989781" units="1" version="1.0"/>
</session>