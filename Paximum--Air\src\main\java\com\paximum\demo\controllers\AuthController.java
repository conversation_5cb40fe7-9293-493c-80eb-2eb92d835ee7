package com.paximum.demo.controllers;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.paximum.demo.models.ApiResponse;
import com.paximum.demo.models.AuthRequest;
import com.paximum.demo.services.AuthService;

import reactor.core.publisher.Mono;

import org.springframework.web.bind.annotation.RequestMapping;

@RestController
@RequestMapping("/auth") // Utiliser /auth comme préfixe
public class AuthController {
    
    private final AuthService authService;

    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping("/login")
    public Mono<ApiResponse> login(@RequestBody AuthRequest authRequest) {
        return authService.authenticate(authRequest)
            .onErrorMap(e -> new RuntimeException("Erreur d'authentification", e)); // Gestion des erreurs
    }
}

