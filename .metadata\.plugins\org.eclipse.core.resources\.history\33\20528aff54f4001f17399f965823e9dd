package com.e_tourism.auth.service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;

import java.util.HashMap;
import java.util.Map;

@Service
public class TourvisioAuthService {

    private final RestTemplate restTemplate;
    
    @Value("${tourvisio.auth-url}")
    private String authUrl;

    @Value("${tourvisio.agency}")
    private String agency;

    @Value("${tourvisio.user}")
    private String user;

    @Value("${tourvisio.password}")
    private String password;

    public TourvisioAuthService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public String authenticate() {
        // Création du payload
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("Agency", agency);
        requestBody.put("User", user);
        requestBody.put("Password", password);

        // Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        // Envoi de la requête POST
        ResponseEntity<Map> response = restTemplate.exchange(authUrl, HttpMethod.POST, requestEntity, Map.class);

        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            return (String) response.getBody().get("token");
        } else {
            throw new RuntimeException("Échec de l'authentification avec Tourvisio");
        }
    }
}

 