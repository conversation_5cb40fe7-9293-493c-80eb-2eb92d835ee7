package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paximum.demo.models.BeginTransactionResponse.Body;
import com.paximum.demo.models.BeginTransactionResponse.Header;
import com.paximum.demo.models.BeginTransactionResponse.Message;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
	 private BeginTransactionResponse.Header header1;
	 private SetReservationInfoResponse.Header header2;
	 private CommitTransactionResponse.Header header3;
	 
	 private BeginTransactionResponse.Body body1;
	 private SetReservationInfoResponse.Body body2;
	 private CommitTransactionResponse.Body body3;
 
    public BeginTransactionResponse.Header getHeader1() {
        return header1;
    }
    public SetReservationInfoResponse.Header getHeader2() {
        return header2;
    }
    public CommitTransactionResponse.Header getHeader3() {
        return header3;
    }

    public BeginTransactionResponse.Body getBody1() {
        return body1;
    }
    public SetReservationInfoResponse.Body getBody2() {
        return body2;
    }
    public CommitTransactionResponse.Body getBody3() {
        return body3;
    }



   

}