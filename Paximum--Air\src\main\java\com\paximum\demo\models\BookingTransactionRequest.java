package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * Classe qui encapsule les différents types de requêtes de transaction de réservation.
 * Utilisée pour l'endpoint /booking-transaction avec paramètre action.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionRequest {

    @JsonProperty("action")
    private String action;

    @JsonProperty("beginRequest")
    private BeginTransactionRequest beginRequest;

    @JsonProperty("infoRequest")
    private SetReservationInfoRequest infoRequest;

    @JsonProperty("commitRequest")
    private CommitTransactionRequest commitRequest;

    // Constructeur par défaut pour Jackson
    public BookingTransactionRequest() {
    }

    // Constructeur pour démarrer une transaction
    public BookingTransactionRequest(BeginTransactionRequest beginRequest) {
        this.action = "begin";
        this.beginRequest = beginRequest;
    }

    // Constructeur pour définir les informations de réservation
    public BookingTransactionRequest(SetReservationInfoRequest infoRequest) {
        this.action = "info";
        this.infoRequest = infoRequest;
    }

    // Constructeur pour finaliser une transaction
    public BookingTransactionRequest(CommitTransactionRequest commitRequest) {
        this.action = "commit";
        this.commitRequest = commitRequest;
    }

    // Getters et Setters
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public BeginTransactionRequest getBeginRequest() {
        return beginRequest;
    }

    public void setBeginRequest(BeginTransactionRequest beginRequest) {
        this.beginRequest = beginRequest;
    }

    public SetReservationInfoRequest getInfoRequest() {
        return infoRequest;
    }

    public void setInfoRequest(SetReservationInfoRequest infoRequest) {
        this.infoRequest = infoRequest;
    }

    public CommitTransactionRequest getCommitRequest() {
        return commitRequest;
    }

    public void setCommitRequest(CommitTransactionRequest commitRequest) {
        this.commitRequest = commitRequest;
    }
}
