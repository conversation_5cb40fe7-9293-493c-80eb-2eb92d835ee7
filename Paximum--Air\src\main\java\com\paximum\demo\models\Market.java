package com.paximum.demo.models;

public class Market {
    private String code;
    private String name;
    private String favicon;
    private String faviconPng;
    
    public Market() {}
    
    public Market(String code, String name, String favicon, String faviconPng) {
        this.code = code;
        this.name = name;
        this.favicon = favicon;
        this.faviconPng = faviconPng;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFavicon() {
        return favicon;
    }
    
    public void setFavicon(String favicon) {
        this.favicon = favicon;
    }
    
    public String getFaviconPng() {
        return faviconPng;
    }
    
    public void setFaviconPng(String faviconPng) {
        this.faviconPng = faviconPng;
    }
}
