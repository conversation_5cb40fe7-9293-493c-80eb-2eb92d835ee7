!SESSION 2025-04-17 14:38:09.818 -----------------------------------------------
eclipse.buildId=4.34.0.202411281622
java.version=21.0.5
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.springframework.boot.ide.branding.sts4
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.springframework.boot.ide.branding.sts4

!ENTRY ch.qos.logback.classic 1 0 2025-04-17 14:38:21.428
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-04-17 14:38:37.164
!MESSAGE Logback config file: C:\Users\<USER>\Desktop\Springboot\Paximum--Air\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-04-17 14:38:38.090
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-17 14:38:38.090
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-04-17 14:38:38.543
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-17 14:38:38.543
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.mylyn.tasks.ui 2 0 2025-04-17 14:38:44.713
!MESSAGE No search provider was registered. Tasks search is not available.

!ENTRY org.eclipse.egit.ui 2 0 2025-04-17 14:38:47.430
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\client\Desktop\Springboot\Paximum--Air\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-04-17 14:39:05.704
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-17 14:39:05.704
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.osgi 4 0 2025-04-17 14:39:06.346
!MESSAGE Application error
!STACK 1
org.eclipse.swt.SWTError: No more handles
	at org.eclipse.swt.SWT.error(SWT.java:4952)
	at org.eclipse.swt.SWT.error(SWT.java:4837)
	at org.eclipse.swt.SWT.error(SWT.java:4808)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:500)
	at org.eclipse.swt.widgets.Control.createHandle(Control.java:675)
	at org.eclipse.swt.widgets.Scrollable.createHandle(Scrollable.java:146)
	at org.eclipse.swt.widgets.Composite.createHandle(Composite.java:300)
	at org.eclipse.swt.widgets.Control.createWidget(Control.java:701)
	at org.eclipse.swt.widgets.Scrollable.createWidget(Scrollable.java:161)
	at org.eclipse.swt.widgets.Control.<init>(Control.java:121)
	at org.eclipse.swt.widgets.Scrollable.<init>(Scrollable.java:85)
	at org.eclipse.swt.widgets.Composite.<init>(Composite.java:103)
	at org.eclipse.ui.splash.BasicSplashHandler.getBundleProgressMonitor(BasicSplashHandler.java:136)
	at org.eclipse.ui.splash.BasicSplashHandler.getContent(BasicSplashHandler.java:222)
	at org.eclipse.ui.internal.splash.EclipseSplashHandler.init(EclipseSplashHandler.java:99)
	at org.eclipse.ui.internal.Workbench$3.run(Workbench.java:842)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.ui.internal.Workbench.createSplashWrapper(Workbench.java:856)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:631)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:570)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:178)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:208)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:143)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:109)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:439)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:271)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:668)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:605)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1481)
!SESSION 2025-04-17 14:39:21.452 -----------------------------------------------
eclipse.buildId=4.34.0.202411281622
java.version=21.0.5
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.springframework.boot.ide.branding.sts4
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.springframework.boot.ide.branding.sts4

!ENTRY ch.qos.logback.classic 1 0 2025-04-17 14:39:23.507
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-04-17 14:39:27.550
!MESSAGE Logback config file: C:\Users\<USER>\Desktop\Springboot\Paximum--Air\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-04-17 14:39:28.116
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-17 14:39:28.116
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-04-17 14:39:28.790
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-17 14:39:28.790
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.mylyn.tasks.ui 2 0 2025-04-17 14:39:30.685
!MESSAGE No search provider was registered. Tasks search is not available.

!ENTRY org.eclipse.egit.ui 2 0 2025-04-17 14:39:31.196
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\client\Downloads\sts-4.27.0.RELEASE\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.5.v20241023-1957\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/Desktop/Springboot/Paximum--Air/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1744893627629
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\Downloads\sts-4.27.0.RELEASE\plugins\org.springframework.tooling.boot.ls_1.59.0.202411281450\servers\spring-boot-language-server\spring-boot-language-server-1.59.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-04-17 14:40:27.630
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-04-17 14:40:27.635
!MESSAGE Started org.springframework.tooling.boot.ls LS process 19968

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:28.453
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:28.466
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:28.851
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:28.856
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:29.341
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:29.345
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-04-17 14:40:29.391
!MESSAGE Classpath changed for project: product_service

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-04-17 14:40:29.392
!MESSAGE Boot project ADDED: product_service

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:30.657
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.m2e.logback.appender 2 0 2025-04-17 14:40:30.666
!MESSAGE Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!

!ENTRY org.eclipse.core.jobs 2 2 2025-04-17 14:40:56.657
!MESSAGE Job found still running after platform shutdown.  Jobs should be canceled by the plugin that scheduled them during shutdown: org.eclipse.lsp4e.LanguageServerWrapper$2 RUNNING
	 at java.base/jdk.internal.misc.Unsafe.park(Native Method)
	 at java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221)
	 at java.base/java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1864)
	 at java.base/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
	 at java.base/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
	 at java.base/java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1898)
	 at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2117)
	 at org.eclipse.lsp4e.LanguageServerWrapper$2.run(LanguageServerWrapper.java:446)
	 at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
!SESSION 2025-04-18 16:42:55.313 -----------------------------------------------
eclipse.buildId=4.34.0.202411281622
java.version=21.0.5
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=fr_FR
Framework arguments:  -product org.springframework.boot.ide.branding.sts4
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.springframework.boot.ide.branding.sts4

!ENTRY ch.qos.logback.classic 1 0 2025-04-18 16:43:41.149
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-04-18 16:44:00.773
!MESSAGE Logback config file: C:\Users\<USER>\Desktop\Springboot\Paximum--Air\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-04-18 16:44:02.326
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-18 16:44:02.326
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-04-18 16:44:02.836
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-04-18 16:44:02.836
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.mylyn.tasks.ui 2 0 2025-04-18 16:44:06.106
!MESSAGE No search provider was registered. Tasks search is not available.

!ENTRY org.eclipse.egit.ui 2 0 2025-04-18 16:44:07.016
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\client\Downloads\sts-4.27.0.RELEASE\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.5.v20241023-1957\jre\bin\javaw.exe
-Dsts.lsp.client=eclipse
-Xmx1024m
-XX:TieredStopAtLevel=1
-Dspring.config.location=classpath:/application.properties
-Xlog:jni+resolve=off
-XX:ErrorFile=C:/Users/<USER>/Desktop/Springboot/Paximum--Air/.metadata/.plugins/org.springframework.tooling.boot.ls/fatal-error-spring-boot_1744987447659
-Dlanguageserver.hover-timeout=225
-jar
C:\Users\<USER>\Downloads\sts-4.27.0.RELEASE\plugins\org.springframework.tooling.boot.ls_1.59.0.202411281450\servers\spring-boot-language-server\spring-boot-language-server-1.59.0-SNAPSHOT-exec.jar
END

!ENTRY org.springframework.tooling.boot.ls 1 0 2025-04-18 16:44:07.661
!MESSAGE DelegatingStreamConnectionProvider - Starting Boot LS

!ENTRY org.springframework.tooling.ls.eclipse.commons 1 0 2025-04-18 16:44:07.690
!MESSAGE Started org.springframework.tooling.boot.ls LS process 15836

!ENTRY org.eclipse.core.jobs 2 2 2025-04-18 16:44:26.320
!MESSAGE Job found still running after platform shutdown.  Jobs should be canceled by the plugin that scheduled them during shutdown: org.eclipse.lsp4e.LanguageServerWrapper$2 RUNNING
	 at java.base/jdk.internal.misc.Unsafe.park(Native Method)
	 at java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221)
	 at java.base/java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1864)
	 at java.base/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
	 at java.base/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
	 at java.base/java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1898)
	 at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2117)
	 at org.eclipse.lsp4e.LanguageServerWrapper$2.run(LanguageServerWrapper.java:446)
	 at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
