package com.e_tourism.auth.service;

import com.e_tourism.auth.dto.LoginRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Base64;
import java.util.Map;

@Service
public class AuthService {

    @Value("${external.api.login.url}")
    private String LOGIN_URL;

    private final RestTemplate restTemplate;

    public AuthService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public ResponseEntity<?> authenticate(LoginRequest loginRequest) {
        try {
            // Préparation des en-têtes
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Si l'API nécessite une authentification de base
            String auth = "yourUsername:yourPassword"; // Remplacer par les identifiants de l'API
            String encodedAuth = new String(Base64.getEncoder().encode(auth.getBytes()));
            headers.set("Authorization", "Basic " + encodedAuth);

            // Création du corps de la requête avec les valeurs reçues
            String requestBody = String.format("""
                {
                    "Agency": "%s",
                    "User": "%s",
                    "Password": "%s"
                }
            """, loginRequest.getAgency(), loginRequest.getUser(), loginRequest.getPassword());

            // Création de la requête HTTP
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // Exécution de la requête POST
            ResponseEntity<Map> response = restTemplate.exchange(LOGIN_URL, HttpMethod.POST, entity, Map.class);

            // Retourner directement la réponse reçue de l’API externe
            return ResponseEntity.ok(response.getBody());

        } catch (HttpClientErrorException e) {
            return ResponseEntity.status(e.getStatusCode()).body(e.getResponseBodyAsString());
        }
    }
}
