package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionRequest {
   
    private BeginTransactionRequest beginRequest;
    private SetReservationInfoRequest infoRequest;
    private CommitTransactionRequest commitRequest;

    // Constructor for beginning a transaction
    public BookingTransactionRequest(BeginTransactionRequest beginRequest) {
        
        this.beginRequest = beginRequest;
    }

    // Constructor for setting reservation info
    public BookingTransactionRequest(SetReservationInfoRequest infoRequest) {
       
        this.infoRequest = infoRequest;
    }

    // Constructor for committing a transaction
    public BookingTransactionRequest(CommitTransactionRequest commitRequest) {
        
        this.commitRequest = commitRequest;
    }

    // Default constructor for Jackson
    public BookingTransactionRequest() {
    }

  

    public BeginTransactionRequest getBeginRequest() {
        return beginRequest;
    }

    public void setBeginRequest(BeginTransactionRequest beginRequest) {
        this.beginRequest = beginRequest;
    }

    public SetReservationInfoRequest getInfoRequest() {
        return infoRequest;
    }

    public void setInfoRequest(SetReservationInfoRequest infoRequest) {
        this.infoRequest = infoRequest;
    }

    public CommitTransactionRequest getCommitRequest() {
        return commitRequest;
    }

    public void setCommitRequest(CommitTransactionRequest commitRequest) {
        this.commitRequest = commitRequest;
    }
}