package com.e_tourism.auth.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.e_tourism.auth.service.TourvisioAuthService;

@RestController
@RequestMapping("/api/tourvisio")
public class TourvisioAuthController {

    private final TourvisioAuthService authService;

    public TourvisioAuthController(TourvisioAuthService authService) {
        this.authService = authService;
    }

    @GetMapping("/auth")
    public String authenticateWithTourvisio() {
        return authService.authenticate();
    }
}
