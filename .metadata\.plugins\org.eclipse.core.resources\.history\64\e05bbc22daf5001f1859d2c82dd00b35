package com.example.demo.dto;

public class AuthResponse {
    private AuthHeader header;
    private AuthBody body;

    public AuthBody getBody() {
        return body;
    }

    // Getters et Setters

    public static class AuthHeader {
        private boolean success;
    }

    public static class AuthBody {
        private String token;
        private String expiresOn;
        private int tokenId;

        public String getToken() {
            return token;
        }
    }
}
