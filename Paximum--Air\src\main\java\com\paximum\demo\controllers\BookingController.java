package com.paximum.demo.controllers;

import org.springframework.web.bind.annotation.*;
import com.paximum.demo.services.BookingService;
import com.paximum.demo.models.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/booking")
@CrossOrigin
public class BookingController {

    private final BookingService bookingService;

    public BookingController(BookingService bookingService) {
        this.bookingService = bookingService;
    }

    /**
     * Point d'entrée unique pour toutes les opérations de transaction de réservation.
     * Traite les requêtes en fonction du champ 'action' dans le corps de la requête.
     *
     * @param request La requête de transaction de réservation contenant le champ 'action'
     * @param token Le token d'authentification
     * @return Une réponse appropriée en fonction de l'action demandée
     */
    @PostMapping(path = "/booking-transaction")
    public Mono<BookingTransactionResponse> processBookingTransaction(
            @RequestBody BookingTransactionRequest request,
            @RequestHeader("Authorization") String token) {

        // Vérifier que l'action est spécifiée dans la requête
        if (request.getAction() == null || request.getAction().isEmpty()) {
            return Mono.error(new RuntimeException("Le champ 'action' est obligatoire dans le corps de la requête"));
        }

        return bookingService.bookingTransaction(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Erreur lors du traitement de la transaction: " + e.getMessage(), e));
    }

    /**
     * Ajoute des services à une réservation (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #addServicesDirect(AddServicesRequest, String)}
     */
    @PostMapping("/add-services")
    public Mono<AddServicesResponse> addServices(
            @RequestBody AddServicesRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.addServices(request, token.replace("Bearer ", ""))
            .onErrorResume(e -> {
                if (e.getMessage() != null && e.getMessage().contains("EncodedOfferIdModel.getSearchId()")) {
                    return Mono.error(new RuntimeException("Invalid offer ID format. The searchId is missing in the encoded offer.", e));
                }
                return Mono.error(new RuntimeException("Failed to add services: " + e.getMessage(), e));
            });
    }

    /**
     * Ajoute des services à une réservation (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/addservices")
    public Mono<AddServicesResponse> addServicesDirect(
            @RequestBody AddServicesRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.addServices(request, token.replace("Bearer ", ""))
            .onErrorResume(e -> {
                if (e.getMessage() != null && e.getMessage().contains("EncodedOfferIdModel.getSearchId()")) {
                    return Mono.error(new RuntimeException("Invalid offer ID format. The searchId is missing in the encoded offer.", e));
                }
                return Mono.error(new RuntimeException("Failed to add services: " + e.getMessage(), e));
            });
    }

    /**
     * Supprime des services d'une réservation (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #removeServicesDirect(RemoveServicesRequest, String)}
     */
    @PostMapping("/remove-services")
    public Mono<RemoveServicesResponse> removeServices(
            @RequestBody RemoveServicesRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.removeServices(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error removing services", e));
    }

    /**
     * Supprime des services d'une réservation (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/removeservices")
    public Mono<RemoveServicesResponse> removeServicesDirect(
            @RequestBody RemoveServicesRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.removeServices(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error removing services", e));
    }



    /**
     * Récupère les détails d'une réservation (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #getReservationDetailDirect(GetReservationDetailRequest, String)}
     */
    @PostMapping("/get-reservation-detail")
    public Mono<GetReservationDetailResponse> getReservationDetail(
            @RequestBody GetReservationDetailRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getReservationDetail(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error calling get-reservation-detail", e));
    }

    /**
     * Récupère les détails d'une réservation (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/getreservationdetail")
    public Mono<GetReservationDetailResponse> getReservationDetailDirect(
            @RequestBody GetReservationDetailRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getReservationDetail(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error calling getreservationdetail", e));
    }

    /**
     * Récupère la liste des réservations (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #getReservationListDirect(GetReservationListRequest, String)}
     */
    @PostMapping("/get-reservation-list")
    public Mono<GetReservationListResponse> getReservationList(
            @RequestBody GetReservationListRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getReservationList(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error fetching reservation list", e));
    }

    /**
     * Récupère la liste des réservations (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/getreservationlist")
    public Mono<GetReservationListResponse> getReservationListDirect(
            @RequestBody GetReservationListRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getReservationList(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error fetching reservation list", e));
    }

    /**
     * Récupère la liste des paiements (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #getPaymentListDirect(GetPaymentListRequest, String)}
     */
    @PostMapping("/get-payment-list")
    public Mono<GetPaymentListResponse> getPaymentList(
            @RequestBody GetPaymentListRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getPaymentList(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error retrieving payment list", e));
    }

    /**
     * Récupère la liste des paiements (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/getpaymentlist")
    public Mono<GetPaymentListResponse> getPaymentListDirect(
            @RequestBody GetPaymentListRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getPaymentList(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error retrieving payment list", e));
    }

    /**
     * Récupère les pénalités d'annulation (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #getCancellationPenaltyDirect(GetCancellationPenaltyRequest, String)}
     */
    @PostMapping("/get-cancellation-penalty")
    public Mono<GetCancellationPenaltyResponse> getCancellationPenalty(
            @RequestBody GetCancellationPenaltyRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getCancellationPenalty(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error getting cancellation penalty", e));
    }

    /**
     * Récupère les pénalités d'annulation (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/getcancellationpenalty")
    public Mono<GetCancellationPenaltyResponse> getCancellationPenaltyDirect(
            @RequestBody GetCancellationPenaltyRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getCancellationPenalty(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error getting cancellation penalty", e));
    }

    /**
     * Annule une réservation (ancienne méthode avec tirets)
     * @deprecated Utiliser plutôt {@link #cancelReservationDirect(CancelReservationRequest, String)}
     */
    @PostMapping("/cancel-reservation")
    public Mono<CancelReservationResponse> cancelReservation(
            @RequestBody CancelReservationRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.cancelReservation(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error cancelling reservation", e));
    }

    /**
     * Annule une réservation (endpoint direct conforme à la documentation Tourvisio)
     */
    @PostMapping("/cancelreservation")
    public Mono<CancelReservationResponse> cancelReservationDirect(
            @RequestBody CancelReservationRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.cancelReservation(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error cancelling reservation", e));
    }
}