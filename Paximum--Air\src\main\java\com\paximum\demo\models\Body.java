package com.paximum.demo.models;

public class Body {
    private String token;
    private String expiresOn;
    private int tokenId;
    private UserInfo userInfo;
    private boolean loggedInWithMasterKey;
    
    public Body() {}
    
    public Body(String token, String expiresOn, int tokenId, UserInfo userInfo, boolean loggedInWithMasterKey) {
        this.token = token;
        this.expiresOn = expiresOn;
        this.tokenId = tokenId;
        this.userInfo = userInfo;
        this.loggedInWithMasterKey = loggedInWithMasterKey;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public String getExpiresOn() {
        return expiresOn;
    }
    
    public void setExpiresOn(String expiresOn) {
        this.expiresOn = expiresOn;
    }
    
    public int getTokenId() {
        return tokenId;
    }
    
    public void setTokenId(int tokenId) {
        this.tokenId = tokenId;
    }
    
    public UserInfo getUserInfo() {
        return userInfo;
    }
    
    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }
    
    public boolean isLoggedInWithMasterKey() {
        return loggedInWithMasterKey;
    }
    
    public void setLoggedInWithMasterKey(boolean loggedInWithMasterKey) {
        this.loggedInWithMasterKey = loggedInWithMasterKey;
    }
}
