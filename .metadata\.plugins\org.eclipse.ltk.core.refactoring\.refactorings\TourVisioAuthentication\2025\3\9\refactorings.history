<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Original element: &apos;TourVisioAuthentication/src/main/java/com.example.auth.service&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth.service" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740838768276" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Original element: &apos;TourVisioAuthentication/src/main/java/com.example.auth.controller&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth.controller" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740838776143" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move element &apos;AuthClient.java&apos; to &apos;TourVisioAuthentication/src/main/java/com.example.auth.config&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Destination element: &apos;TourVisioAuthentication/src/main/java/com.example.auth.config&apos;&#x0D;&#x0A;- Original element: &apos;com.example.auth.AuthClient.java&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move compilation unit" destination="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth.config" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth{AuthClient.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.move" patterns="*" policy="org.eclipse.jdt.ui.moveResources" qualified="false" references="true" stamp="1740838898971" units="1" version="1.0"/>&#x0A;<refactoring comment="Move element &apos;AuthController.java&apos; to &apos;TourVisioAuthentication/src/main/java/com.example.auth.controllers&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Destination element: &apos;TourVisioAuthentication/src/main/java/com.example.auth.controllers&apos;&#x0D;&#x0A;- Original element: &apos;com.example.auth.AuthController.java&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move compilation unit" destination="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth.controllers" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth{AuthController.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.move" patterns="*" policy="org.eclipse.jdt.ui.moveResources" qualified="false" references="true" stamp="1740838921749" units="1" version="1.0"/>&#x0A;<refactoring comment="Move element &apos;AuthService.java&apos; to &apos;TourVisioAuthentication/src/main/java/com.example.auth.services&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuthentication&apos;&#x0D;&#x0A;- Destination element: &apos;TourVisioAuthentication/src/main/java/com.example.auth.services&apos;&#x0D;&#x0A;- Original element: &apos;com.example.auth.AuthService.java&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move compilation unit" destination="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth.services" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.auth{AuthService.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.move" patterns="*" policy="org.eclipse.jdt.ui.moveResources" qualified="false" references="true" stamp="1740838927346" units="1" version="1.0"/>
</session>