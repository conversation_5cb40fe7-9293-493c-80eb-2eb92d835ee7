<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<section name="WorkbenchPreferenceDialog.dialogBounds">
		<item key="DIALOG_X_ORIGIN" value="371"/>
		<item key="DIALOG_Y_ORIGIN" value="137"/>
		<item key="DIALOG_WIDTH" value="1381"/>
		<item key="DIALOG_HEIGHT" value="889"/>
		<item key="DIALOG_FONT_NAME" value="1|Segoe UI|9.0|0|WINDOWS|1|-15|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI"/>
	</section>
	<section name="org.eclipse.ui.internal.QuickAccess">
		<item key="org.eclipse.ui.internal.quickaccess.QuickAccessDialogDIALOG_WIDTH" value="600"/>
		<item key="org.eclipse.ui.internal.quickaccess.QuickAccessDialogDIALOG_HEIGHT" value="675"/>
		<item key="org.eclipse.ui.internal.quickaccess.QuickAccessDialogDIALOG_USE_PERSISTED_SIZE" value="true"/>
		<item key="org.eclipse.ui.internal.quickaccess.QuickAccessDialogDIALOG_USE_PERSISTED_LOCATION" value="false"/>
		<list key="orderedElements">
			<item value="spring.initializr.addStarters"/>
			<item value="org.eclipse.pde.ui.openDependencies"/>
		</list>
		<list key="orderedProviders">
			<item value="org.eclipse.ui.commands"/>
			<item value="org.eclipse.ui.commands"/>
		</list>
		<list key="textEntries">
			<item value="2"/>
			<item value="1"/>
		</list>
		<list key="textArray">
			<item value="depe"/>
			<item value="dep"/>
			<item value="depend"/>
		</list>
	</section>
	<section name="NewWizardAction">
		<item key="NewWizardSelectionPage.STORE_SELECTED_ID" value="org.eclipse.m2e.core.wizards.Maven2ModuleWizard"/>
		<list key="NewWizardSelectionPage.STORE_EXPANDED_CATEGORIES_ID">
			<item value="org.eclipse.m2e"/>
		</list>
		<section name="NewWizard.dialogBounds">
			<item key="DIALOG_X_ORIGIN" value="619"/>
			<item key="DIALOG_Y_ORIGIN" value="194"/>
			<item key="DIALOG_WIDTH" value="700"/>
			<item key="DIALOG_HEIGHT" value="505"/>
			<item key="DIALOG_FONT_NAME" value="1|Segoe UI|9.0|0|WINDOWS|1|-15|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI"/>
		</section>
	</section>
</section>
