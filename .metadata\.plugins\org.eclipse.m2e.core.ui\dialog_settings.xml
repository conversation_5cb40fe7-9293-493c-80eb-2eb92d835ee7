<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<section name="org.eclipse.m2e.core.pom.overview">
	</section>
	<section name="org.eclipse.m2e.core.pom.dependencies">
	</section>
	<section name="MavenProjectWizardLocationPage">
		<list key="location">
			<item value="C:\Users\<USER>\Desktop\Springboot\Paximum-Air\product-service"/>
			<item value="C:\Users\<USER>\Desktop\Springboot"/>
			<item value="C:\Users\<USER>\Desktop\Springboot\product_service"/>
		</list>
		<list key="projectNameTemplate">
			<item value="[artifactId]"/>
			<item value="[artifactId]-TRUNK"/>
			<item value="[artifactId]-[version]"/>
			<item value="[groupId].[artifactId]"/>
			<item value="[groupId].[artifactId]-[version]"/>
			<item value="[name]"/>
		</list>
	</section>
	<section name="MavenProjectWizardArchetypePage">
		<item key="catalog" value="All Catalogs"/>
		<list key="projectNameTemplate">
			<item value="[artifactId]"/>
			<item value="[artifactId]-TRUNK"/>
			<item value="[artifactId]-[version]"/>
			<item value="[groupId].[artifactId]"/>
			<item value="[groupId].[artifactId]-[version]"/>
			<item value="[name]"/>
		</list>
	</section>
	<section name="Maven2ProjectWizardArchifactPage">
		<list key="package">
			<item value="org.product_service"/>
		</list>
		<list key="groupId">
			<item value="com.paximum"/>
		</list>
		<list key="artifactId">
			<item value="product_service"/>
		</list>
		<list key="projectNameTemplate">
			<item value="[artifactId]"/>
			<item value="[artifactId]-TRUNK"/>
			<item value="[artifactId]-[version]"/>
			<item value="[groupId].[artifactId]"/>
			<item value="[groupId].[artifactId]-[version]"/>
			<item value="[name]"/>
		</list>
		<list key="version">
			<item value="0.0.1-SNAPSHOT"/>
		</list>
	</section>
	<section name="MavenProjectWizardArtifactPage">
		<list key="groupId">
			<item value="com.paximum"/>
			<item value="com.paximum.auth"/>
		</list>
		<list key="name">
		</list>
		<list key="artifactId">
			<item value="product_service"/>
			<item value="PaximumAir"/>
			<item value="authentication_service"/>
			<item value="authentication-service"/>
			<item value="Paximum-Air"/>
			<item value="product-service"/>
		</list>
		<list key="projectNameTemplate">
			<item value="[artifactId]"/>
			<item value="[artifactId]-TRUNK"/>
			<item value="[artifactId]-[version]"/>
			<item value="[groupId].[artifactId]"/>
			<item value="[groupId].[artifactId]-[version]"/>
			<item value="[name]"/>
		</list>
		<list key="version">
			<item value="0.0.1-SNAPSHOT"/>
		</list>
	</section>
	<section name="MavenModuleWizardParentPage">
		<list key="moduleName">
			<item value="product_service"/>
			<item value="authentication_service"/>
			<item value="authentication-service"/>
			<item value="product-service"/>
		</list>
		<list key="projectNameTemplate">
			<item value="[artifactId]"/>
			<item value="[artifactId]-TRUNK"/>
			<item value="[artifactId]-[version]"/>
			<item value="[groupId].[artifactId]"/>
			<item value="[groupId].[artifactId]-[version]"/>
			<item value="[name]"/>
		</list>
	</section>
</section>
