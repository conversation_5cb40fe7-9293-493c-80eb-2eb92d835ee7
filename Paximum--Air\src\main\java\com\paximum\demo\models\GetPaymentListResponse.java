package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPaymentListResponse {
    private Header header;
    private Body body;

    public GetPaymentListResponse() {}

    public Header getHeader() { return header; }
    public void setHeader(Header header) { this.header = header; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }

    public static class Header {
        private String requestId;
        private Boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {}

        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        public Boolean getSuccess() { return success; }
        public void setSuccess(Boolean success) { this.success = success; }
        public String getResponseTime() { return responseTime; }
        public void setResponseTime(String responseTime) { this.responseTime = responseTime; }
        public List<Message> getMessages() { return messages; }
        public void setMessages(List<Message> messages) { this.messages = messages; }
    }

    public static class Message {
        private Integer id;
        private String code;
        private Integer messageType;
        private String message;

        public Message() {}

        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getMessageType() { return messageType; }
        public void setMessageType(Integer messageType) { this.messageType = messageType; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class Body {
        private List<Reservation> reservations;
        private Integer totalCount;
        private Integer maxIndexNumber;
        private Integer minIndexNumber;

        public Body() {}

        public List<Reservation> getReservations() { return reservations; }
        public void setReservations(List<Reservation> reservations) { this.reservations = reservations; }
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public Integer getMaxIndexNumber() { return maxIndexNumber; }
        public void setMaxIndexNumber(Integer maxIndexNumber) { this.maxIndexNumber = maxIndexNumber; }
        public Integer getMinIndexNumber() { return minIndexNumber; }
        public void setMinIndexNumber(Integer minIndexNumber) { this.minIndexNumber = minIndexNumber; }
    }

    public static class Reservation {
        private String reservationNumber;
        private String beginDate;
        private String endDate;
        private Integer night;
        private Integer reservationStatus;
        private String registerDate;
        private String leaderName;
        private Integer adult;
        private Integer child;
        private Price salePrice;
        private Double agencyCommission;
        private Double agencyDiscountFromComm;
        private Double discountFromPassenger;
        private Double discountFromAgency;
        private Double agencySupplementComm;
        private Double agencyEarlyBooking;
        private Double passengerEarlyBooking;
        private Double agencyPayable;
        private Double agencyPayableAccordingToDueDate;
        private Double agencyPayment;
        private Integer passengerBonus;
        private Double usedPassengerBonus;
        private Double agencyBonus;
        private Double usedAgencyBonus;
        private Double userBonus;
        private Double usedUserBonus;
        private String agency;
        private String agencyUser;
        private Integer confirmationStatus;
        private City departureCity;
        private City arrivalCity;
        private Double balance;
        private Integer paymentStatus;
        private Double agencyAmountToPay;
        private Double passengerAmountToPay;
        private Integer rowNumber;

        public Reservation() {}

        public String getReservationNumber() { return reservationNumber; }
        public void setReservationNumber(String reservationNumber) { this.reservationNumber = reservationNumber; }
        public String getBeginDate() { return beginDate; }
        public void setBeginDate(String beginDate) { this.beginDate = beginDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public Integer getNight() { return night; }
        public void setNight(Integer night) { this.night = night; }
        public Integer getReservationStatus() { return reservationStatus; }
        public void setReservationStatus(Integer reservationStatus) { this.reservationStatus = reservationStatus; }
        public String getRegisterDate() { return registerDate; }
        public void setRegisterDate(String registerDate) { this.registerDate = registerDate; }
        public String getLeaderName() { return leaderName; }
        public void setLeaderName(String leaderName) { this.leaderName = leaderName; }
        public Integer getAdult() { return adult; }
        public void setAdult(Integer adult) { this.adult = adult; }
        public Integer getChild() { return child; }
        public void setChild(Integer child) { this.child = child; }
        public Price getSalePrice() { return salePrice; }
        public void setSalePrice(Price salePrice) { this.salePrice = salePrice; }
        public Double getAgencyCommission() { return agencyCommission; }
        public void setAgencyCommission(Double agencyCommission) { this.agencyCommission = agencyCommission; }
        public Double getAgencyDiscountFromComm() { return agencyDiscountFromComm; }
        public void setAgencyDiscountFromComm(Double agencyDiscountFromComm) { this.agencyDiscountFromComm = agencyDiscountFromComm; }
        public Double getDiscountFromPassenger() { return discountFromPassenger; }
        public void setDiscountFromPassenger(Double discountFromPassenger) { this.discountFromPassenger = discountFromPassenger; }
        public Double getDiscountFromAgency() { return discountFromAgency; }
        public void setDiscountFromAgency(Double discountFromAgency) { this.discountFromAgency = discountFromAgency; }
        public Double getAgencySupplementComm() { return agencySupplementComm; }
        public void setAgencySupplementComm(Double agencySupplementComm) { this.agencySupplementComm = agencySupplementComm; }
        public Double getAgencyEarlyBooking() { return agencyEarlyBooking; }
        public void setAgencyEarlyBooking(Double agencyEarlyBooking) { this.agencyEarlyBooking = agencyEarlyBooking; }
        public Double getPassengerEarlyBooking() { return passengerEarlyBooking; }
        public void setPassengerEarlyBooking(Double passengerEarlyBooking) { this.passengerEarlyBooking = passengerEarlyBooking; }
        public Double getAgencyPayable() { return agencyPayable; }
        public void setAgencyPayable(Double agencyPayable) { this.agencyPayable = agencyPayable; }
        public Double getAgencyPayableAccordingToDueDate() { return agencyPayableAccordingToDueDate; }
        public void setAgencyPayableAccordingToDueDate(Double agencyPayableAccordingToDueDate) { this.agencyPayableAccordingToDueDate = agencyPayableAccordingToDueDate; }
        public Double getAgencyPayment() { return agencyPayment; }
        public void setAgencyPayment(Double agencyPayment) { this.agencyPayment = agencyPayment; }
        public Integer getPassengerBonus() { return passengerBonus; }
        public void setPassengerBonus(Integer passengerBonus) { this.passengerBonus = passengerBonus; }
        public Double getUsedPassengerBonus() { return usedPassengerBonus; }
        public void setUsedPassengerBonus(Double usedPassengerBonus) { this.usedPassengerBonus = usedPassengerBonus; }
        public Double getAgencyBonus() { return agencyBonus; }
        public void setAgencyBonus(Double agencyBonus) { this.agencyBonus = agencyBonus; }
        public Double getUsedAgencyBonus() { return usedAgencyBonus; }
        public void setUsedAgencyBonus(Double usedAgencyBonus) { this.usedAgencyBonus = usedAgencyBonus; }
        public Double getUserBonus() { return userBonus; }
        public void setUserBonus(Double userBonus) { this.userBonus = userBonus; }
        public Double getUsedUserBonus() { return usedUserBonus; }
        public void setUsedUserBonus(Double usedUserBonus) { this.usedUserBonus = usedUserBonus; }
        public String getAgency() { return agency; }
        public void setAgency(String agency) { this.agency = agency; }
        public String getAgencyUser() { return agencyUser; }
        public void setAgencyUser(String agencyUser) { this.agencyUser = agencyUser; }
        public Integer getConfirmationStatus() { return confirmationStatus; }
        public void setConfirmationStatus(Integer confirmationStatus) { this.confirmationStatus = confirmationStatus; }
        public City getDepartureCity() { return departureCity; }
        public void setDepartureCity(City departureCity) { this.departureCity = departureCity; }
        public City getArrivalCity() { return arrivalCity; }
        public void setArrivalCity(City arrivalCity) { this.arrivalCity = arrivalCity; }
        public Double getBalance() { return balance; }
        public void setBalance(Double balance) { this.balance = balance; }
        public Integer getPaymentStatus() { return paymentStatus; }
        public void setPaymentStatus(Integer paymentStatus) { this.paymentStatus = paymentStatus; }
        public Double getAgencyAmountToPay() { return agencyAmountToPay; }
        public void setAgencyAmountToPay(Double agencyAmountToPay) { this.agencyAmountToPay = agencyAmountToPay; }
        public Double getPassengerAmountToPay() { return passengerAmountToPay; }
        public void setPassengerAmountToPay(Double passengerAmountToPay) { this.passengerAmountToPay = passengerAmountToPay; }
        public Integer getRowNumber() { return rowNumber; }
        public void setRowNumber(Integer rowNumber) { this.rowNumber = rowNumber; }
    }

    public static class Price {
        private Double amount;
        private String currency;

        public Price() {}

        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }

    public static class City {
        private String name;
        private Integer provider;
        private Boolean isTopRegion;
        private String id;

        public City() {}

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public Boolean getIsTopRegion() { return isTopRegion; }
        public void setIsTopRegion(Boolean isTopRegion) { this.isTopRegion = isTopRegion; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
    }
}