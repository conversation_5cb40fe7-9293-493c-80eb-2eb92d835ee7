<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Copy element &apos;priceMethodeClient.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.config.priceMethodeClient.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.config{priceMethodeClient.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741776020285" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;priceMethodeController.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.controllers.priceMethodeController.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.controllers" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.controllers{priceMethodeController.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741776027102" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;priceMethodeService.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.auth.services&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.auth.services&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.services.priceMethodeService.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.services" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.services{priceMethodeService.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741776045279" units="1" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;PriceSearchRequest.java&apos; to &apos;Paximum--Air/src/main/java/com.paximum.auth.models&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java/com.paximum.auth.models&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.models.PriceSearchRequest.java&apos;" description="Copy compilation unit" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.models{PriceSearchRequest.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741776338322" units="1" version="1.0"/>
</session>