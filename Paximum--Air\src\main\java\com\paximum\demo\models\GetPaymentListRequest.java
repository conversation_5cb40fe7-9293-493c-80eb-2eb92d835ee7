package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPaymentListRequest {
    private String currency;
    private String culture;
    private String dueDate;
    private List<DateCriteria> dateCriterias;
    private Boolean includeDueDate;
    private Integer limit;
    private Boolean isRequestEmpty;
    private Boolean getReservations;
    private Integer pageRowCount;
    private Integer minIndexNumber;
    private Integer maxIndexNumber;

    // Default constructor
    public GetPaymentListRequest() {}

    // Getters and Setters
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
    public String getCulture() { return culture; }
    public void setCulture(String culture) { this.culture = culture; }
    public String getDueDate() { return dueDate; }
    public void setDueDate(String dueDate) { this.dueDate = dueDate; }
    public List<DateCriteria> getDateCriterias() { return dateCriterias; }
    public void setDateCriterias(List<DateCriteria> dateCriterias) { this.dateCriterias = dateCriterias; }
    public Boolean getIncludeDueDate() { return includeDueDate; }
    public void setIncludeDueDate(Boolean includeDueDate) { this.includeDueDate = includeDueDate; }
    public Integer getLimit() { return limit; }
    public void setLimit(Integer limit) { this.limit = limit; }
    public Boolean getIsRequestEmpty() { return isRequestEmpty; }
    public void setIsRequestEmpty(Boolean isRequestEmpty) { this.isRequestEmpty = isRequestEmpty; }
    public Boolean getGetReservations() { return getReservations; }
    public void setGetReservations(Boolean getReservations) { this.getReservations = getReservations; }
    public Integer getPageRowCount() { return pageRowCount; }
    public void setPageRowCount(Integer pageRowCount) { this.pageRowCount = pageRowCount; }
    public Integer getMinIndexNumber() { return minIndexNumber; }
    public void setMinIndexNumber(Integer minIndexNumber) { this.minIndexNumber = minIndexNumber; }
    public Integer getMaxIndexNumber() { return maxIndexNumber; }
    public void setMaxIndexNumber(Integer maxIndexNumber) { this.maxIndexNumber = maxIndexNumber; }

    // Nested static class for DateCriteria
    public static class DateCriteria {
        private Integer type;
        private String from;
        private String to;

        public DateCriteria() {}

        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getFrom() { return from; }
        public void setFrom(String from) { this.from = from; }
        public String getTo() { return to; }
        public void setTo(String to) { this.to = to; }
    }
}