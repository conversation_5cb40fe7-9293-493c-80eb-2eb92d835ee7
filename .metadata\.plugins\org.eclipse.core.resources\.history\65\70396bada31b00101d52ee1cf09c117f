package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paximum.demo.models.BeginTransactionResponse.Body;
import com.paximum.demo.models.BeginTransactionResponse.Header;
import com.paximum.demo.models.BeginTransactionResponse.Message;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
	 private BeginTransactionResponse.Header header1;
	 private SetReservationInfoResponse.Header header2;
	 private CommitTransactionResponse.Header header3;
	 
	 private BeginTransactionResponse.Body body1;
	 private SetReservationInfoResponse.Body body2;
	 private CommitTransactionResponse.Body body3;
    private String action;
    private Object data;
    private boolean success;
    private String message;
    public BeginTransactionResponse.Header getHeader1() {
        return header1;
    }
    public SetReservationInfoResponse.Header getHeader2() {
        return header2;
    }
    public CommitTransactionResponse.Header getHeader3() {
        return header3;
    }

    public BeginTransactionResponse.Body getBody1() {
        return body1;
    }
    public SetReservationInfoResponse.Body getBody2() {
        return body2;
    }
    public CommitTransactionResponse.Body getBody3() {
        return body3;
    }



   
    // Constructor for begin transaction response
    public BookingTransactionResponse(String action, Object data, boolean success, String message) {
        this.action = action;
        this.data = data;
        this.success = success;
        this.message = message;
    }

    // Default constructor for Jackson
    public BookingTransactionResponse() {
    }

    // Getters and Setters
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    // Helper methods to retrieve specific response types
    public BeginTransactionResponse getBeginResponse() {
        if (data instanceof BeginTransactionResponse) {
            return (BeginTransactionResponse) data;
        }
        return null;
    }

    public SetReservationInfoResponse getInfoResponse() {
        if (data instanceof SetReservationInfoResponse) {
            return (SetReservationInfoResponse) data;
        }
        return null;
    }

    public CommitTransactionResponse getCommitResponse() {
        if (data instanceof CommitTransactionResponse.Header) {
            return (CommitTransactionResponse) data;
        }
        return null;
    }
}