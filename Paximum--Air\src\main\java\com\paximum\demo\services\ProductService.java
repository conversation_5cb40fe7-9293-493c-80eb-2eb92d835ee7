package com.paximum.demo.services;

import org.springframework.stereotype.Service;

import com.paximum.demo.config.ProductClient;
import com.paximum.demo.models.ApiResponsePService;
import com.paximum.demo.models.PriceSearchRequest;
import com.paximum.demo.models.GetOffersRequest;
import com.paximum.demo.models.GetOffersResponse;

import reactor.core.publisher.Mono;

@Service
public class ProductService {

    private final ProductClient productClient;

    public ProductService(ProductClient productClient) {
        this.productClient = productClient;
    }

    // Méthode existante pour searchPrice
    public Mono<ApiResponsePService> searchPrice(PriceSearchRequest request, String token) {
        return productClient.searchPrice(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error in remote price search"+ e.getMessage(), e)));
    }

    // Nouvelle méthode pour getOffers
    public Mono<GetOffersResponse> getOffers(GetOffersRequest request, String token) {
        return productClient.getOffers(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error in remote get offers"+ e.getMessage(), e)));
    }
}