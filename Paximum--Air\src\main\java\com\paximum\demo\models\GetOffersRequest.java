package com.paximum.demo.models;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetOffersRequest {
    
    @JsonProperty("productType")
    private int productType;
    
    @JsonProperty("searchId")
    private String searchId;
    
    @JsonProperty("offerIds")
    private List<String> offerIds;
    
    @JsonProperty("currency")
    private String currency;
    
    @JsonProperty("culture")
    private String culture;

    // Constructeur par défaut
    public GetOffersRequest() {}

    // Constructeur complet
    public GetOffersRequest(int productType, String searchId, List<String> offerIds, String currency, String culture) {
        this.productType = productType;
        this.searchId = searchId;
        this.offerIds = offerIds;
        this.currency = currency;
        this.culture = culture;
    }

    // Getters et setters
    public int getProductType() {
        return productType;
    }
    
    public void setProductType(int productType) {
        this.productType = productType;
    }
    
    public String getSearchId() {
        return searchId;
    }
    
    public void setSearchId(String searchId) {
        this.searchId = searchId;
    }
    
    public List<String> getOfferIds() {
        return offerIds;
    }
    
    public void setOfferIds(List<String> offerIds) {
        this.offerIds = offerIds;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getCulture() {
        return culture;
    }
    
    public void setCulture(String culture) {
        this.culture = culture;
    }
}
