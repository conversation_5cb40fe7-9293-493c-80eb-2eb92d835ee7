package com.paximum.demo.services;

import org.springframework.stereotype.Service;

import com.paximum.demo.config.AuthClient;
import com.paximum.demo.models.ApiResponse;
import com.paximum.demo.models.AuthRequest;

import reactor.core.publisher.Mono;

@Service
public class AuthService {

    private final AuthClient authClient;

    public AuthService(AuthClient authClient) {
        this.authClient = authClient;
    }

    public Mono<ApiResponse> authenticate(AuthRequest authRequest) {
        return authClient.authenticate(authRequest)
            .onErrorResume(e -> Mono.error(new RuntimeException("Erreur d'authentification avec le service distant", e))); 
    }
}
