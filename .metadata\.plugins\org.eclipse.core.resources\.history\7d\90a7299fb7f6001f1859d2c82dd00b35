package com.example.auth.services;

import org.springframework.stereotype.Service;

import com.example.auth.config.AuthClient;

import reactor.core.publisher.Mono;

import java.util.Map;

@Service
public class AuthService {

    private final AuthClient authClient;

    public AuthService(AuthClient authClient) {
        this.authClient = authClient;
    }

    public Mono<Map<String, Object>> authenticate(Map<String, String> authRequest) {
        return authClient.authenticate(authRequest);
    }
}
