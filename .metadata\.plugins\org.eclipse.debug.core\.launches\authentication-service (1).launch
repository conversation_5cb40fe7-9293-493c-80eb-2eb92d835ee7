<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.eclipse.m2e.Maven2LaunchConfigurationType">
    <intAttribute key="M2_COLORS" value="0"/>
    <booleanAttribute key="M2_DEBUG_OUTPUT" value="false"/>
    <stringAttribute key="M2_GOALS" value=""/>
    <booleanAttribute key="M2_NON_RECURSIVE" value="false"/>
    <booleanAttribute key="M2_OFFLINE" value="false"/>
    <stringAttribute key="M2_PROFILES" value=""/>
    <listAttribute key="M2_PROPERTIES"/>
    <stringAttribute key="M2_RUNTIME" value="EMBEDDED"/>
    <booleanAttribute key="M2_SKIP_TESTS" value="false"/>
    <intAttribute key="M2_THREADS" value="1"/>
    <booleanAttribute key="M2_UPDATE_SNAPSHOTS" value="false"/>
    <stringAttribute key="M2_USER_SETTINGS" value=""/>
    <booleanAttribute key="M2_WORKSPACE_RESOLUTION" value="false"/>
    <booleanAttribute key="org.eclipse.debug.core.ATTR_FORCE_SYSTEM_CONSOLE_ENCODING" value="false"/>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_ATTR_USE_ARGFILE" value="false"/>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_SHOW_CODEDETAILS_IN_EXCEPTION_MESSAGES" value="true"/>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_USE_CLASSPATH_ONLY_JAR" value="false"/>
    <stringAttribute key="org.eclipse.jdt.launching.WORKING_DIRECTORY" value="${project_loc:authentication-service}"/>
</launchConfiguration>
