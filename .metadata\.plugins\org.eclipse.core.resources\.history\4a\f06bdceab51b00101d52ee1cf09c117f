package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommitTransactionResponse {
    public static Header header;
    public static Body body;

    public CommitTransactionResponse() {}

    public Header getHeader() { return header; }
    public void setHeader(Header header) { this.header = header; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }

    public static class Header {
        private String requestId;
        private Boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {}

        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        public Boolean getSuccess() { return success; }
        public void setSuccess(Boolean success) { this.success = success; }
        public String getResponseTime() { return responseTime; }
        public void setResponseTime(String responseTime) { this.responseTime = responseTime; }
        public List<Message> getMessages() { return messages; }
        public void setMessages(List<Message> messages) { this.messages = messages; }
    }

    public static class Message {
        private Integer id;
        private String code;
        private Integer messageType;
        private String message;

        public Message() {}

        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getMessageType() { return messageType; }
        public void setMessageType(Integer messageType) { this.messageType = messageType; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class Body {
        private String reservationNumber;
        private String encryptedReservationNumber;
        private String transactionId;

        public Body() {}

        public String getReservationNumber() { return reservationNumber; }
        public void setReservationNumber(String reservationNumber) { this.reservationNumber = reservationNumber; }
        public String getEncryptedReservationNumber() { return encryptedReservationNumber; }
        public void setEncryptedReservationNumber(String encryptedReservationNumber) { this.encryptedReservationNumber = encryptedReservationNumber; }
        public String getTransactionId() { return transactionId; }
        public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
    }
}