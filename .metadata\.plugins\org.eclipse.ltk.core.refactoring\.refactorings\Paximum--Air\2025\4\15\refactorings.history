<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename compilation unit &apos;com.paximum.demo.models.flightBrandInfo.java&apos; to &apos;FlightBrandInfo.java&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.flightBrandInfo.java&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.FlightBrandInfo.java&apos;" description="Rename compilation unit &apos;flightBrandInfo.java&apos;" flags="2" id="org.eclipse.jdt.ui.rename.compilationunit" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{flightBrandInfo.java" name="FlightBrandInfo.java" references="false" stamp="1744273104035" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.models.FlightBrandInfo&apos; to &apos;FlightBrandInfo_PService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.FlightBrandInfo&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.models.FlightBrandInfo_PService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;FlightBrandInfo&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightBrandInfo.java[FlightBrandInfo" matchStrategy="1" name="FlightBrandInfo_PService" qualified="false" references="true" similarDeclarations="false" stamp="1744273127825" textual="false" version="1.0"/>&#x0A;&#x0A;&#x0A;<refactoring accessors="true" comment="Delete 17 elements from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original elements:&#x0D;&#x0A;     com.paximum.demo.models.Airline_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Airport_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Body_PService.java&#x0D;&#x0A;     com.paximum.demo.models.City_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Country_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Flight_PService.java&#x0D;&#x0A;     com.paximum.demo.models.FlightBrandInfo_PService.java&#x0D;&#x0A;     com.paximum.demo.models.FlightClass_PService.java&#x0D;&#x0A;     com.paximum.demo.models.FlightProvider_PService.java&#x0D;&#x0A;     com.paximum.demo.models.GeoLocation_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Header_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Item_PService.java&#x0D;&#x0A;     com.paximum.demo.models.LocationInfo_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Message_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Passenger_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Segment_PService.java&#x0D;&#x0A;     com.paximum.demo.models.Service_PService.java" description="Delete elements" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Country_PService.java" element10="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Airline_PService.java" element11="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Segment_PService.java" element12="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{LocationInfo_PService.java" element13="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightProvider_PService.java" element14="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Passenger_PService.java" element15="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Flight_PService.java" element16="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{GeoLocation_PService.java" element17="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Airport_PService.java" element2="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Item_PService.java" element3="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Header_PService.java" element4="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightClass_PService.java" element5="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Message_PService.java" element6="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Body_PService.java" element7="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{Service_PService.java" element8="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{City_PService.java" element9="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{FlightBrandInfo_PService.java" elements="17" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1744274190179" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.BaggageInformation_PService.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{BaggageInformation_PService.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1744287623848" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.models.PriceSearchResponse.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.models{PriceSearchResponse.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1744287645741" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.config.JacksonConfig.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.config{JacksonConfig.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1744389491438" subPackages="false" version="1.0"/>
</session>