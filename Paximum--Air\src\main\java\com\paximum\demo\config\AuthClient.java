package com.paximum.demo.config;

import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import com.paximum.demo.models.ApiResponse;
import com.paximum.demo.models.AuthRequest;

import reactor.core.publisher.Mono;

@Component
public class AuthClient {

    private final WebClient webClient;

    public AuthClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl("http://service.stage.paximum.com/v2/api/authenticationservice").build();
    }

    public Mono<ApiResponse> authenticate(AuthRequest authRequest) {
        return this.webClient.post()
            .uri("/login")
            .bodyValue(authRequest)
            .retrieve()
            .bodyToMono(ApiResponse.class);
    }
}
 