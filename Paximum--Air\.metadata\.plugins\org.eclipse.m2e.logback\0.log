2025-04-17 14:38:47,287 [Worker-1: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is not available. Remote download required.
2025-04-17 14:39:31,170 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-04-17 14:40:20,504 [Worker-16: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-maven-plugin/maven-metadata.xml
2025-04-17 14:40:20,537 [Worker-16: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-maven-plugin/maven-metadata.xml
2025-04-17 14:40:20,725 [Worker-16: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-maven-plugin/3.4.4/spring-boot-maven-plugin-3.4.4.pom
2025-04-17 14:40:20,763 [Worker-16: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-maven-plugin/3.4.4/spring-boot-maven-plugin-3.4.4.pom
2025-04-17 14:40:20,944 [Worker-16: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-maven-plugin/3.4.4/spring-boot-maven-plugin-3.4.4.jar
2025-04-17 14:40:21,649 [Worker-16: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot-maven-plugin/3.4.4/spring-boot-maven-plugin-3.4.4.jar
2025-04-17 14:40:21,671 [Worker-16: Building] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using NULL lifecycle mapping for MavenProject: com.paximum:PaximumAir:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\pom.xml.
2025-04-17 14:40:21,907 [Worker-4: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using NULL lifecycle mapping for MavenProject: com.paximum:PaximumAir:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\pom.xml.
2025-04-17 14:40:22,763 [Worker-4: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:product-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\product_service\pom.xml.
2025-04-17 14:40:24,604 [Worker-16: Building] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:authentication-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\authentication_service\pom.xml.
2025-04-17 14:40:28,452 [Worker-16: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:28,457 [Worker-16: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 1 resource from src\main\resources to target\classes
2025-04-17 14:40:28,465 [Worker-16: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:28,466 [Worker-16: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 0 resource from src\test\resources to target\test-classes
2025-04-17 14:40:28,850 [Worker-2: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:28,852 [Worker-2: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 1 resource from src\main\resources to target\classes
2025-04-17 14:40:28,855 [Worker-2: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:28,856 [Worker-2: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 0 resource from src\test\resources to target\test-classes
2025-04-17 14:40:29,110 [Worker-2: Building] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:product-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\product_service\pom.xml.
2025-04-17 14:40:29,340 [Worker-2: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:29,341 [Worker-2: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 1 resource from src\main\resources to target\classes
2025-04-17 14:40:29,345 [Worker-2: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:29,346 [Worker-2: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 0 resource from src\test\resources to target\test-classes
2025-04-17 14:40:29,586 [Worker-10: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using NULL lifecycle mapping for MavenProject: com.paximum:PaximumAir:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\pom.xml.
2025-04-17 14:40:29,717 [Worker-10: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:product-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\product_service\pom.xml.
2025-04-17 14:40:29,950 [Worker-10: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:authentication-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\authentication_service\pom.xml.
2025-04-17 14:40:30,657 [Worker-10: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:30,658 [Worker-10: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 1 resource from src\main\resources to target\classes
2025-04-17 14:40:30,666 [Worker-10: Building] WARN  o.a.m.s.f.DefaultMavenResourcesFiltering - Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
2025-04-17 14:40:30,667 [Worker-10: Building] INFO  o.a.m.s.f.DefaultMavenResourcesFiltering - Copying 0 resource from src\test\resources to target\test-classes
2025-04-17 14:40:42,157 [Worker-9: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:product-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\product_service\pom.xml.
2025-04-17 14:40:42,329 [Worker-9: Updating Maven Dependencies] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.paximum:authentication-service:0.0.1-SNAPSHOT @ C:\Users\<USER>\Desktop\Springboot\PaximumAir\authentication_service\pom.xml.
2025-04-18 16:44:06,759 [Worker-7: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
