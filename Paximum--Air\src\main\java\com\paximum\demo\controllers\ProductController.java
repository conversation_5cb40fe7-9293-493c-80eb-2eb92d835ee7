package com.paximum.demo.controllers;

import com.paximum.demo.models.ApiResponsePService;
import com.paximum.demo.models.PriceSearchRequest;
import com.paximum.demo.models.GetOffersRequest;
import com.paximum.demo.models.GetOffersResponse;
import com.paximum.demo.services.ProductService;

import reactor.core.publisher.Mono;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/product")
public class ProductController {
    
    private final ProductService productService;

    public ProductController(ProductService productService) {
        this.productService = productService;
    }

    // Endpoint existant pour searchPrice
    @PostMapping("/pricesearch")
    public Mono<ApiResponsePService> searchPrice(
            @RequestBody PriceSearchRequest request,
            @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.replace("Bearer ", "");
        return productService.searchPrice(request, token)
            .onErrorMap(e -> new RuntimeException("Error during price search", e));
    }

    // Nouvel endpoint pour getOffers
    @PostMapping("/getoffers")
    public Mono<GetOffersResponse> getOffers(
            @RequestBody GetOffersRequest request,
            @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.replace("Bearer ", "");
        return productService.getOffers(request, token)
            .onErrorMap(e -> new RuntimeException("Error during get offers", e));
    }
}