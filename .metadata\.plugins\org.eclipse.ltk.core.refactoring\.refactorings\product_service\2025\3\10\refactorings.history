<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Move package &apos;com.example.prod.config&apos; to &apos;product-service/src/main/java/com.paximum.productservice&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;product-service/src/main/java/com.paximum.productservice&apos;&#x0D;&#x0A;- Original element: &apos;product_service/src/main/java/com.example.prod.config&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product-service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.prod.config" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741002095689" version="1.0"/>&#x0A;<refactoring comment="Move element &apos;priceMethodeClient.java&apos; to &apos;product_service/src/main/java/com.example.prod.config&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java/com.example.prod.config&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.config.priceMethodeClient.java&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move compilation unit" destination="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.prod.config" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.config{priceMethodeClient.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.move" patterns="*" policy="org.eclipse.jdt.ui.moveResources" qualified="false" references="true" stamp="1741002254870" units="1" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;product_service&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Original element: &apos;product_service/src/main/java/com.paximum.productservice.config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.config" element2="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice" element3="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum" elements="3" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741002259102" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;static&apos; to &apos;authentication-service/src/main/resources&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication-service/src/main/resources&apos;&#x0D;&#x0A;- Original element: &apos;static&apos;" description="Copy folder" destination="=authentication-service/src\/main\/resources=/maven.pomderived=/true=/=/optional=/true=/" element1="src/main/resources/static" files="0" flags="589830" folders="1" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741003484688" units="0" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;application.properties&apos; to &apos;authentication-service/src/main/resources&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication-service/src/main/resources&apos;&#x0D;&#x0A;- Original element: &apos;application.properties&apos;" description="Copy file" destination="=authentication-service/src\/main\/resources=/maven.pomderived=/true=/=/optional=/true=/" element1="src/main/resources/application.properties" files="1" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1741003525603" units="0" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.productservice.models.AuthRequest&apos; to &apos;PriceSearchRequest&apos;&#x0D;&#x0A;- Original project: &apos;product_service&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.models.AuthRequest&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.productservice.models.PriceSearchRequest&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;AuthRequest&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.models{AuthRequest.java[AuthRequest" matchStrategy="1" name="PriceSearchRequest" qualified="false" references="true" similarDeclarations="false" stamp="1741450182480" textual="false" version="1.0"/>
</session>