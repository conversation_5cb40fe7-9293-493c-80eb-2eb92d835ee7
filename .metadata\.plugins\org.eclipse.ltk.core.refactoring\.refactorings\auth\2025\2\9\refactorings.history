<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;auth/src/main/java/com.e_tourism.auth.config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.e_tourism.auth.config" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740583010215" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;com.e_tourism.auth.service.TourvisioAuthService.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.e_tourism.auth.service{TourvisioAuthService.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740583220700" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;com.e_tourism.auth.controller.TourvisioAuthController.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.e_tourism.auth.controller{TourvisioAuthController.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740583229891" subPackages="false" version="1.0"/>&#x0A;&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;LoginRequest.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;{LoginRequest.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740584727865" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;com.e_tourism.auth.LoginRequest.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.e_tourism.auth{LoginRequest.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740584951188" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;com.e_tourism.auth.config.AppConfig.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.e_tourism.auth.config{AppConfig.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740585206727" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;auth&apos;&#x0D;&#x0A;- Original project: &apos;auth&apos;&#x0D;&#x0A;- Original element: &apos;auth/src/main/java/com.e_tourism.auth.config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.e_tourism.auth.config" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740585209486" subPackages="false" version="1.0"/>
</session>