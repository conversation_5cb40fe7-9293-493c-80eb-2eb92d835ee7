package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCancellationPenaltyResponse {
    private Header header;
    private Body body;

    public GetCancellationPenaltyResponse() {}

    public Header getHeader() { return header; }
    public void setHeader(Header header) { this.header = header; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }

    public static class Body {
        private List<CancelPenalty> cancelPenalties;

        public List<CancelPenalty> getCancelPenalties() { return cancelPenalties; }
        public void setCancelPenalties(List<CancelPenalty> cancelPenalties) { this.cancelPenalties = cancelPenalties; }
    }

    public static class CancelPenalty {
        private Reason reason;
        private List<Service> services;
        private Price price;
        private Boolean isCancelable;

        public Reason getReason() { return reason; }
        public void setReason(Reason reason) { this.reason = reason; }
        public List<Service> getServices() { return services; }
        public void setServices(List<Service> services) { this.services = services; }
        public Price getPrice() { return price; }
        public void setPrice(Price price) { this.price = price; }
        public Boolean getIsCancelable() { return isCancelable; }
        public void setIsCancelable(Boolean isCancelable) { this.isCancelable = isCancelable; }
    }

    public static class Reason {
        private String id;
        private String name;
        private String comment;

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
    }

    public static class Service {
        private Integer provider;
        private String id;
        private String code;
        private Integer productType;
        private String name;
        private Price price;
        private Boolean isCancelable;
        private List<String> relatedServices;
        private PriceDetail priceDetail;

        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getProductType() { return productType; }
        public void setProductType(Integer productType) { this.productType = productType; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Price getPrice() { return price; }
        public void setPrice(Price price) { this.price = price; }
        public Boolean getIsCancelable() { return isCancelable; }
        public void setIsCancelable(Boolean isCancelable) { this.isCancelable = isCancelable; }
        public List<String> getRelatedServices() { return relatedServices; }
        public void setRelatedServices(List<String> relatedServices) { this.relatedServices = relatedServices; }
        public PriceDetail getPriceDetail() { return priceDetail; }
        public void setPriceDetail(PriceDetail priceDetail) { this.priceDetail = priceDetail; }
    }

    public static class PriceDetail {
        private Price totalSalePrice;
        private Price penalty;
        private Price mainServiceFee;

        public Price getTotalSalePrice() { return totalSalePrice; }
        public void setTotalSalePrice(Price totalSalePrice) { this.totalSalePrice = totalSalePrice; }
        public Price getPenalty() { return penalty; }
        public void setPenalty(Price penalty) { this.penalty = penalty; }
        public Price getMainServiceFee() { return mainServiceFee; }
        public void setMainServiceFee(Price mainServiceFee) { this.mainServiceFee = mainServiceFee; }
    }
}
