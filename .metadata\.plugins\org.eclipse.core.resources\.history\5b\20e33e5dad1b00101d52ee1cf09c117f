package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BeginTransactionRequest {

    @JsonProperty("offerIds")
    private List<String> offerIds;
    
    @JsonProperty("reservationNumber")
    private String reservationNumber;
    
    @JsonProperty("currency")
    private String currency;
    
    @JsonProperty("culture")
    private String culture;

    // Constructeur par défaut pour Jackson
    public BeginTransactionRequest() {
    }

    // Constructeur pour le cas d'une nouvelle transaction avec offerIds
    public BeginTransactionRequest(List<String> offerIds, String currency, String culture) {
        this.offerIds = offerIds;
        this.currency = currency;
        this.culture = culture;
    }

    // Constructeur pour le cas d'une transaction existante (avec reservationNumber)
    public BeginTransactionRequest(String reservationNumber, String currency, String culture) {
        this.reservationNumber = reservationNumber;
        this.currency = currency;
        this.culture = culture;
    }

    public List<String> getOfferIds() {
        return offerIds;
    }

    public void setOfferIds(List<String> offerIds) {
        this.offerIds = offerIds;
    }

    public String getReservationNumber() {
        return reservationNumber;
    }

    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCulture() {
        return culture;
    }

    public void setCulture(String culture) {
        this.culture = culture;
    }
}
