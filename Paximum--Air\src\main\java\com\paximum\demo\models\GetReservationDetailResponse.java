package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetReservationDetailResponse {

    private Header header;
    private Body body;

    // Constructeurs
    public GetReservationDetailResponse() {}

    public GetReservationDetailResponse(Header header, Body body) {
        this.header = header;
        this.body = body;
    }

    // Getters et Setters
    public Header getHeader() { return header; }
    public void setHeader(Header header) { this.header = header; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }

    // Classe statique Header
    public static class Header {
        private String requestId;
        private Boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {}

        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        public Boolean getSuccess() { return success; }
        public void setSuccess(Boolean success) { this.success = success; }
        public String getResponseTime() { return responseTime; }
        public void setResponseTime(String responseTime) { this.responseTime = responseTime; }
        public List<Message> getMessages() { return messages; }
        public void setMessages(List<Message> messages) { this.messages = messages; }
    }

    // Classe statique Message
    public static class Message {
        private Integer id;
        private String code;
        private Integer messageType;
        private String message;

        public Message() {}

        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getMessageType() { return messageType; }
        public void setMessageType(Integer messageType) { this.messageType = messageType; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    // Classe statique Body
    public static class Body {
        private String reservationNumber;
        private String encryptedReservationNumber;
        private String transactionId;
        private ReservationData reservationData;
        private Integer status;

        public Body() {}

        public String getReservationNumber() { return reservationNumber; }
        public void setReservationNumber(String reservationNumber) { this.reservationNumber = reservationNumber; }
        public String getEncryptedReservationNumber() { return encryptedReservationNumber; }
        public void setEncryptedReservationNumber(String encryptedReservationNumber) { this.encryptedReservationNumber = encryptedReservationNumber; }
        public String getTransactionId() { return transactionId; }
        public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
        public ReservationData getReservationData() { return reservationData; }
        public void setReservationData(ReservationData reservationData) { this.reservationData = reservationData; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    // Classe statique ReservationData
    public static class ReservationData {
        private List<Traveller> travellers;
        private ReservationInfo reservationInfo;
        private List<Service> services;
        private PaymentDetail paymentDetail;
        private List<Object> invoices;

        public ReservationData() {}

        public List<Traveller> getTravellers() { return travellers; }
        public void setTravellers(List<Traveller> travellers) { this.travellers = travellers; }
        public ReservationInfo getReservationInfo() { return reservationInfo; }
        public void setReservationInfo(ReservationInfo reservationInfo) { this.reservationInfo = reservationInfo; }
        public List<Service> getServices() { return services; }
        public void setServices(List<Service> services) { this.services = services; }
        public PaymentDetail getPaymentDetail() { return paymentDetail; }
        public void setPaymentDetail(PaymentDetail paymentDetail) { this.paymentDetail = paymentDetail; }
        public List<Object> getInvoices() { return invoices; }
        public void setInvoices(List<Object> invoices) { this.invoices = invoices; }
    }

    // Classe statique Traveller
    public static class Traveller {
        private String travellerId;
        private Integer type;
        private Integer title;
        private List<Title> availableTitles;
        private AcademicTitle academicTitle;
        private List<AcademicTitle> availableAcademicTitles;
        private String name;
        private String surname;
        private Boolean isLeader;
        private String birthDate;
        private Integer age;
        private Nationality nationality;
        private String identityNumber;
        private PassportInfo passportInfo;
        private Address address;
        private Object destinationAddress;
        private List<Service> services;
        private Integer gender;
        private Integer orderNumber;
        private List<String> requiredFields;
        private List<Object> documents;
        private Integer passengerType;
        private Map<String, String> additionalFields;
        private List<Object> insertFields;
        private Integer status;

        public Traveller() {}

        public String getTravellerId() { return travellerId; }
        public void setTravellerId(String travellerId) { this.travellerId = travellerId; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public Integer getTitle() { return title; }
        public void setTitle(Integer title) { this.title = title; }
        public List<Title> getAvailableTitles() { return availableTitles; }
        public void setAvailableTitles(List<Title> availableTitles) { this.availableTitles = availableTitles; }
        public AcademicTitle getAcademicTitle() { return academicTitle; }
        public void setAcademicTitle(AcademicTitle academicTitle) { this.academicTitle = academicTitle; }
        public List<AcademicTitle> getAvailableAcademicTitles() { return availableAcademicTitles; }
        public void setAvailableAcademicTitles(List<AcademicTitle> availableAcademicTitles) { this.availableAcademicTitles = availableAcademicTitles; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getSurname() { return surname; }
        public void setSurname(String surname) { this.surname = surname; }
        public Boolean getIsLeader() { return isLeader; }
        public void setIsLeader(Boolean isLeader) { this.isLeader = isLeader; }
        public String getBirthDate() { return birthDate; }
        public void setBirthDate(String birthDate) { this.birthDate = birthDate; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public Nationality getNationality() { return nationality; }
        public void setNationality(Nationality nationality) { this.nationality = nationality; }
        public String getIdentityNumber() { return identityNumber; }
        public void setIdentityNumber(String identityNumber) { this.identityNumber = identityNumber; }
        public PassportInfo getPassportInfo() { return passportInfo; }
        public void setPassportInfo(PassportInfo passportInfo) { this.passportInfo = passportInfo; }
        public Address getAddress() { return address; }
        public void setAddress(Address address) { this.address = address; }
        public Object getDestinationAddress() { return destinationAddress; }
        public void setDestinationAddress(Object destinationAddress) { this.destinationAddress = destinationAddress; }
        public List<Service> getServices() { return services; }
        public void setServices(List<Service> services) { this.services = services; }
        public Integer getGender() { return gender; }
        public void setGender(Integer gender) { this.gender = gender; }
        public Integer getOrderNumber() { return orderNumber; }
        public void setOrderNumber(Integer orderNumber) { this.orderNumber = orderNumber; }
        public List<String> getRequiredFields() { return requiredFields; }
        public void setRequiredFields(List<String> requiredFields) { this.requiredFields = requiredFields; }
        public List<Object> getDocuments() { return documents; }
        public void setDocuments(List<Object> documents) { this.documents = documents; }
        public Integer getPassengerType() { return passengerType; }
        public void setPassengerType(Integer passengerType) { this.passengerType = passengerType; }
        public Map<String, String> getAdditionalFields() { return additionalFields; }
        public void setAdditionalFields(Map<String, String> additionalFields) { this.additionalFields = additionalFields; }
        public List<Object> getInsertFields() { return insertFields; }
        public void setInsertFields(List<Object> insertFields) { this.insertFields = insertFields; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    // Classe statique Title
    public static class Title {
        private String id;
        private String name;

        public Title() {}

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    // Classe statique AcademicTitle
    public static class AcademicTitle {
        private String id;
        private String name;

        public AcademicTitle() {}

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    // Classe statique Nationality
    public static class Nationality {
        private String name;
        private String twoLetterCode;
        private String threeLetterCode;
        private String numericCode;
        private String isdCode;

        public Nationality() {}

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getTwoLetterCode() { return twoLetterCode; }
        public void setTwoLetterCode(String twoLetterCode) { this.twoLetterCode = twoLetterCode; }
        public String getThreeLetterCode() { return threeLetterCode; }
        public void setThreeLetterCode(String threeLetterCode) { this.threeLetterCode = threeLetterCode; }
        public String getNumericCode() { return numericCode; }
        public void setNumericCode(String numericCode) { this.numericCode = numericCode; }
        public String getIsdCode() { return isdCode; }
        public void setIsdCode(String isdCode) { this.isdCode = isdCode; }
    }

    // Classe statique PassportInfo
    public static class PassportInfo {
        private String serial;
        private String number;
        private String expireDate;
        private String issueDate;
        private String citizenshipCountryCode;

        public PassportInfo() {}

        public String getSerial() { return serial; }
        public void setSerial(String serial) { this.serial = serial; }
        public String getNumber() { return number; }
        public void setNumber(String number) { this.number = number; }
        public String getExpireDate() { return expireDate; }
        public void setExpireDate(String expireDate) { this.expireDate = expireDate; }
        public String getIssueDate() { return issueDate; }
        public void setIssueDate(String issueDate) { this.issueDate = issueDate; }
        public String getCitizenshipCountryCode() { return citizenshipCountryCode; }
        public void setCitizenshipCountryCode(String citizenshipCountryCode) { this.citizenshipCountryCode = citizenshipCountryCode; }
    }

    // Classe statique Address
    public static class Address {
        private Phone contactPhone;
        private String phone;
        private String email;
        private String address;
        private String zipCode;
        private City city;
        private Country country;
        private List<String> addressLines;

        public Address() {}

        public Phone getContactPhone() { return contactPhone; }
        public void setContactPhone(Phone contactPhone) { this.contactPhone = contactPhone; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        public String getZipCode() { return zipCode; }
        public void setZipCode(String zipCode) { this.zipCode = zipCode; }
        public City getCity() { return city; }
        public void setCity(City city) { this.city = city; }
        public Country getCountry() { return country; }
        public void setCountry(Country country) { this.country = country; }
        public List<String> getAddressLines() { return addressLines; }
        public void setAddressLines(List<String> addressLines) { this.addressLines = addressLines; }
    }

    // Classe statique Phone
    public static class Phone {
        private String countryCode;
        private String phoneNumber;

        public Phone() {}

        public String getCountryCode() { return countryCode; }
        public void setCountryCode(String countryCode) { this.countryCode = countryCode; }
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
    }

    // Classe statique City
    public static class City {
        private String id;
        private String name;
        private String latitude;
        private String longitude;
        private Integer provider;
        private Boolean isTopRegion;
        private String code;
        private Integer type;
        private String parentId;
        private String countryId;

        public City() {}

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getLatitude() { return latitude; }
        public void setLatitude(String latitude) { this.latitude = latitude; }
        public String getLongitude() { return longitude; }
        public void setLongitude(String longitude) { this.longitude = longitude; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public Boolean getIsTopRegion() { return isTopRegion; }
        public void setIsTopRegion(Boolean isTopRegion) { this.isTopRegion = isTopRegion; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getParentId() { return parentId; }
        public void setParentId(String parentId) { this.parentId = parentId; }
        public String getCountryId() { return countryId; }
        public void setCountryId(String countryId) { this.countryId = countryId; }
    }

    // Classe statique Country
    public static class Country {
        private String id;
        private String name;
        private String internationalCode;
        private Integer provider;
        private Boolean isTopRegion;
        private String code;
        private Integer type;
        private String latitude;
        private String longitude;
        private String parentId;
        private String countryId;

        public Country() {}

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getInternationalCode() { return internationalCode; }
        public void setInternationalCode(String internationalCode) { this.internationalCode = internationalCode; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public Boolean getIsTopRegion() { return isTopRegion; }
        public void setIsTopRegion(Boolean isTopRegion) { this.isTopRegion = isTopRegion; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getLatitude() { return latitude; }
        public void setLatitude(String latitude) { this.latitude = latitude; }
        public String getLongitude() { return longitude; }
        public void setLongitude(String longitude) { this.longitude = longitude; }
        public String getParentId() { return parentId; }
        public void setParentId(String parentId) { this.parentId = parentId; }
        public String getCountryId() { return countryId; }
        public void setCountryId(String countryId) { this.countryId = countryId; }
    }

    // Classe statique Service
    public static class Service {
        private String id;
        private Integer type;
        private String ticketNo;
        private Price price;
        private Integer passengerType;
        private Integer orderNumber;
        private String note;
        private Country departureCountry;
        private City departureCity;
        private Country arrivalCountry;
        private City arrivalCity;
        private ServiceDetails serviceDetails;
        private Boolean isMainService;
        private Boolean isRefundable;
        private Boolean bundle;
        private List<CancellationPolicy> cancellationPolicies;
        private List<Document> documents;
        private String providerBookingID;
        private String supplierBookingNumber;
        private String encryptedServiceNumber;
        private List<Object> priceBreakDowns;
        private Integer commission;
        private ReservableInfo reservableInfo;
        private Integer unit;
        private Integer confirmationStatus;
        private Integer serviceStatus;
        private Integer productType;
        private Boolean createServiceTypeIfNotExists;
        private String code;
        private String name;
        private String beginDate;
        private String endDate;
        private Integer adult;
        private Integer child;
        private Integer infant;
        private Boolean includePackage;
        private Boolean compulsory;
        private Boolean isExtraService;
        private Integer provider;
        private List<String> travellers;
        private Boolean thirdPartyRecord;
        private Integer recordId;
        private Map<String, String> additionalFields;

        public Service() {}

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getTicketNo() { return ticketNo; }
        public void setTicketNo(String ticketNo) { this.ticketNo = ticketNo; }
        public Price getPrice() { return price; }
        public void setPrice(Price price) { this.price = price; }
        public Integer getPassengerType() { return passengerType; }
        public void setPassengerType(Integer passengerType) { this.passengerType = passengerType; }
        public Integer getOrderNumber() { return orderNumber; }
        public void setOrderNumber(Integer orderNumber) { this.orderNumber = orderNumber; }
        public String getNote() { return note; }
        public void setNote(String note) { this.note = note; }
        public Country getDepartureCountry() { return departureCountry; }
        public void setDepartureCountry(Country departureCountry) { this.departureCountry = departureCountry; }
        public City getDepartureCity() { return departureCity; }
        public void setDepartureCity(City departureCity) { this.departureCity = departureCity; }
        public Country getArrivalCountry() { return arrivalCountry; }
        public void setArrivalCountry(Country arrivalCountry) { this.arrivalCountry = arrivalCountry; }
        public City getArrivalCity() { return arrivalCity; }
        public void setArrivalCity(City arrivalCity) { this.arrivalCity = arrivalCity; }
        public ServiceDetails getServiceDetails() { return serviceDetails; }
        public void setServiceDetails(ServiceDetails serviceDetails) { this.serviceDetails = serviceDetails; }
        public Boolean getIsMainService() { return isMainService; }
        public void setIsMainService(Boolean isMainService) { this.isMainService = isMainService; }
        public Boolean getIsRefundable() { return isRefundable; }
        public void setIsRefundable(Boolean isRefundable) { this.isRefundable = isRefundable; }
        public Boolean getBundle() { return bundle; }
        public void setBundle(Boolean bundle) { this.bundle = bundle; }
        public List<CancellationPolicy> getCancellationPolicies() { return cancellationPolicies; }
        public void setCancellationPolicies(List<CancellationPolicy> cancellationPolicies) { this.cancellationPolicies = cancellationPolicies; }
        public List<Document> getDocuments() { return documents; }
        public void setDocuments(List<Document> documents) { this.documents = documents; }
        public String getProviderBookingID() { return providerBookingID; }
        public void setProviderBookingID(String providerBookingID) { this.providerBookingID = providerBookingID; }
        public String getSupplierBookingNumber() { return supplierBookingNumber; }
        public void setSupplierBookingNumber(String supplierBookingNumber) { this.supplierBookingNumber = supplierBookingNumber; }
        public String getEncryptedServiceNumber() { return encryptedServiceNumber; }
        public void setEncryptedServiceNumber(String encryptedServiceNumber) { this.encryptedServiceNumber = encryptedServiceNumber; }
        public List<Object> getPriceBreakDowns() { return priceBreakDowns; }
        public void setPriceBreakDowns(List<Object> priceBreakDowns) { this.priceBreakDowns = priceBreakDowns; }
        public Integer getCommission() { return commission; }
        public void setCommission(Integer commission) { this.commission = commission; }
        public ReservableInfo getReservableInfo() { return reservableInfo; }
        public void setReservableInfo(ReservableInfo reservableInfo) { this.reservableInfo = reservableInfo; }
        public Integer getUnit() { return unit; }
        public void setUnit(Integer unit) { this.unit = unit; }
        public Integer getConfirmationStatus() { return confirmationStatus; }
        public void setConfirmationStatus(Integer confirmationStatus) { this.confirmationStatus = confirmationStatus; }
        public Integer getServiceStatus() { return serviceStatus; }
        public void setServiceStatus(Integer serviceStatus) { this.serviceStatus = serviceStatus; }
        public Integer getProductType() { return productType; }
        public void setProductType(Integer productType) { this.productType = productType; }
        public Boolean getCreateServiceTypeIfNotExists() { return createServiceTypeIfNotExists; }
        public void setCreateServiceTypeIfNotExists(Boolean createServiceTypeIfNotExists) { this.createServiceTypeIfNotExists = createServiceTypeIfNotExists; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getBeginDate() { return beginDate; }
        public void setBeginDate(String beginDate) { this.beginDate = beginDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public Integer getAdult() { return adult; }
        public void setAdult(Integer adult) { this.adult = adult; }
        public Integer getChild() { return child; }
        public void setChild(Integer child) { this.child = child; }
        public Integer getInfant() { return infant; }
        public void setInfant(Integer infant) { this.infant = infant; }
        public Boolean getIncludePackage() { return includePackage; }
        public void setIncludePackage(Boolean includePackage) { this.includePackage = includePackage; }
        public Boolean getCompulsory() { return compulsory; }
        public void setCompulsory(Boolean compulsory) { this.compulsory = compulsory; }
        public Boolean getIsExtraService() { return isExtraService; }
        public void setIsExtraService(Boolean isExtraService) { this.isExtraService = isExtraService; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public List<String> getTravellers() { return travellers; }
        public void setTravellers(List<String> travellers) { this.travellers = travellers; }
        public Boolean   getThirdPartyRecord() { return thirdPartyRecord; }
        public void setThirdPartyRecord(Boolean thirdPartyRecord) { this.thirdPartyRecord = thirdPartyRecord; }
        public Integer getRecordId() { return recordId; }
        public void setRecordId(Integer recordId) { this.recordId = recordId; }
        public Map<String, String> getAdditionalFields() { return additionalFields; }
        public void setAdditionalFields(Map<String, String> additionalFields) { this.additionalFields = additionalFields; }
    }

    // Classe statique Price
    public static class Price {
        private Double amount;
        private String currency;

        public Price() {}

        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }

    // Classe statique ReservationInfo
    public static class ReservationInfo {
        private String bookingNumber;
        private String encryptedBookingNumber;
        private Market market;
        private Operator operator;
        private Agency agency;
        private AgencyUser agencyUser;
        private String beginDate;
        private String endDate;
        private String note;
        private String agencyReservationNumber;
        private Price salePrice;
        private Price supplementDiscount;
        private Price passengerEB;
        private Price agencyEB;
        private Price passengerAmountToPay;
        private Price agencyAmountToPay;
        private Price discount;
        private Price agencyBalance;
        private Price passengerBalance;
        private Commission agencyCommission;
        private Commission brokerCommission;
        private Commission agencySupplementCommission;
        private Price promotionAmount;
        private Price priceToPay;
        private Price agencyPriceToPay;
        private Price passengerPriceToPay;
        private Price totalPrice;
        private Integer reservationStatus;
        private Integer confirmationStatus;
        private Integer paymentStatus;
        private List<Document> documents;
        private List<Object> otherDocuments;
        private ReservableInfo reservableInfo;
        private Integer paymentFrom;
        private Location departureCountry;
        private Location departureCity;
        private Location arrivalCountry;
        private Location arrivalCity;
        private String createDate;
        private String changeDate;
        private Map<String, String> additionalFields;
        private String additionalCode1;
        private String additionalCode2;
        private String additionalCode3;
        private String additionalCode4;
        private Integer agencyDiscount;
        private Boolean hasAvailablePromotionCode;

        public ReservationInfo() {}

        public String getBookingNumber() { return bookingNumber; }
        public void setBookingNumber(String bookingNumber) { this.bookingNumber = bookingNumber; }
        public String getEncryptedBookingNumber() { return encryptedBookingNumber; }
        public void setEncryptedBookingNumber(String encryptedBookingNumber) { this.encryptedBookingNumber = encryptedBookingNumber; }
        public Market getMarket() { return market; }
        public void setMarket(Market market) { this.market = market; }
        public Operator getOperator() { return operator; }
        public void setOperator(Operator operator) { this.operator = operator; }
        public Agency getAgency() { return agency; }
        public void setAgency(Agency agency) { this.agency = agency; }
        public AgencyUser getAgencyUser() { return agencyUser; }
        public void setAgencyUser(AgencyUser agencyUser) { this.agencyUser = agencyUser; }
        public String getBeginDate() { return beginDate; }
        public void setBeginDate(String beginDate) { this.beginDate = beginDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public String getNote() { return note; }
        public void setNote(String note) { this.note = note; }
        public String getAgencyReservationNumber() { return agencyReservationNumber; }
        public void setAgencyReservationNumber(String agencyReservationNumber) { this.agencyReservationNumber = agencyReservationNumber; }
        public Price getSalePrice() { return salePrice; }
        public void setSalePrice(Price salePrice) { this.salePrice = salePrice; }
        public Price getSupplementDiscount() { return supplementDiscount; }
        public void setSupplementDiscount(Price supplementDiscount) { this.supplementDiscount = supplementDiscount; }
        public Price getPassengerEB() { return passengerEB; }
        public void setPassengerEB(Price passengerEB) { this.passengerEB = passengerEB; }
        public Price getAgencyEB() { return agencyEB; }
        public void setAgencyEB(Price agencyEB) { this.agencyEB = agencyEB; }
        public Price getPassengerAmountToPay() { return passengerAmountToPay; }
        public void setPassengerAmountToPay(Price passengerAmountToPay) { this.passengerAmountToPay = passengerAmountToPay; }
        public Price getAgencyAmountToPay() { return agencyAmountToPay; }
        public void setAgencyAmountToPay(Price agencyAmountToPay) { this.agencyAmountToPay = agencyAmountToPay; }
        public Price getDiscount() { return discount; }
        public void setDiscount(Price discount) { this.discount = discount; }
        public Price getAgencyBalance() { return agencyBalance; }
        public void setAgencyBalance(Price agencyBalance) { this.agencyBalance = agencyBalance; }
        public Price getPassengerBalance() { return passengerBalance; }
        public void setPassengerBalance(Price passengerBalance) { this.passengerBalance = passengerBalance; }
        public Commission getAgencyCommission() { return agencyCommission; }
        public void setAgencyCommission(Commission agencyCommission) { this.agencyCommission = agencyCommission; }
        public Commission getBrokerCommission() { return brokerCommission; }
        public void setBrokerCommission(Commission brokerCommission) { this.brokerCommission = brokerCommission; }
        public Commission getAgencySupplementCommission() { return agencySupplementCommission; }
        public void setAgencySupplementCommission(Commission agencySupplementCommission) { this.agencySupplementCommission = agencySupplementCommission; }
        public Price getPromotionAmount() { return promotionAmount; }
        public void setPromotionAmount(Price promotionAmount) { this.promotionAmount = promotionAmount; }
        public Price getPriceToPay() { return priceToPay; }
        public void setPriceToPay(Price priceToPay) { this.priceToPay = priceToPay; }
        public Price getAgencyPriceToPay() { return agencyPriceToPay; }
        public void setAgencyPriceToPay(Price agencyPriceToPay) { this.agencyPriceToPay = agencyPriceToPay; }
        public Price getPassengerPriceToPay() { return passengerPriceToPay; }
        public void setPassengerPriceToPay(Price passengerPriceToPay) { this.passengerPriceToPay = passengerPriceToPay; }
        public Price getTotalPrice() { return totalPrice; }
        public void setTotalPrice(Price totalPrice) { this.totalPrice = totalPrice; }
        public Integer getReservationStatus() { return reservationStatus; }
        public void setReservationStatus(Integer reservationStatus) { this.reservationStatus = reservationStatus; }
        public Integer getConfirmationStatus() { return confirmationStatus; }
        public void setConfirmationStatus(Integer confirmationStatus) { this.confirmationStatus = confirmationStatus; }
        public Integer getPaymentStatus() { return paymentStatus; }
        public void setPaymentStatus(Integer paymentStatus) { this.paymentStatus = paymentStatus; }
        public List<Document> getDocuments() { return documents; }
        public void setDocuments(List<Document> documents) { this.documents = documents; }
        public List<Object> getOtherDocuments() { return otherDocuments; }
        public void setOtherDocuments(List<Object> otherDocuments) { this.otherDocuments = otherDocuments; }
        public ReservableInfo getReservableInfo() { return reservableInfo; }
        public void setReservableInfo(ReservableInfo reservableInfo) { this.reservableInfo = reservableInfo; }
        public Integer getPaymentFrom() { return paymentFrom; }
        public void setPaymentFrom(Integer paymentFrom) { this.paymentFrom = paymentFrom; }
        public Location getDepartureCountry() { return departureCountry; }
        public void setDepartureCountry(Location departureCountry) { this.departureCountry = departureCountry; }
        public Location getDepartureCity() { return departureCity; }
        public void setDepartureCity(Location departureCity) { this.departureCity = departureCity; }
        public Location getArrivalCountry() { return arrivalCountry; }
        public void setArrivalCountry(Location arrivalCountry) { this.arrivalCountry = arrivalCountry; }
        public Location getArrivalCity() { return arrivalCity; }
        public void setArrivalCity(Location arrivalCity) { this.arrivalCity = arrivalCity; }
        public String getCreateDate() { return createDate; }
        public void setCreateDate(String createDate) { this.createDate = createDate; }
        public String getChangeDate() { return changeDate; }
        public void setChangeDate(String changeDate) { this.changeDate = changeDate; }
        public Map<String, String> getAdditionalFields() { return additionalFields; }
        public void setAdditionalFields(Map<String, String> additionalFields) { this.additionalFields = additionalFields; }
        public String getAdditionalCode1() { return additionalCode1; }
        public void setAdditionalCode1(String additionalCode1) { this.additionalCode1 = additionalCode1; }
        public String getAdditionalCode2() { return additionalCode2; }
        public void setAdditionalCode2(String additionalCode2) { this.additionalCode2 = additionalCode2; }
        public String getAdditionalCode3() { return additionalCode3; }
        public void setAdditionalCode3(String additionalCode3) { this.additionalCode3 = additionalCode3; }
        public String getAdditionalCode4() { return additionalCode4; }
        public void setAdditionalCode4(String additionalCode4) { this.additionalCode4 = additionalCode4; }
        public Integer getAgencyDiscount() { return agencyDiscount; }
        public void setAgencyDiscount(Integer agencyDiscount) { this.agencyDiscount = agencyDiscount; }
        public Boolean getHasAvailablePromotionCode() { return hasAvailablePromotionCode; }
        public void setHasAvailablePromotionCode(Boolean hasAvailablePromotionCode) { this.hasAvailablePromotionCode = hasAvailablePromotionCode; }
    }

    // Classe statique Market
    public static class Market {
        private String code;
        private String name;

        public Market() {}

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    // Classe statique Operator
    public static class Operator {
        private String code;
        private String name;
        private Boolean agencyCanDiscountCommission;

        public Operator() {}

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Boolean getAgencyCanDiscountCommission() { return agencyCanDiscountCommission; }
        public void setAgencyCanDiscountCommission(Boolean agencyCanDiscountCommission) { this.agencyCanDiscountCommission = agencyCanDiscountCommission; }
    }

    // Classe statique Agency
    public static class Agency {
        private String code;
        private String name;
        private Country country;
        private Address address;
        private Boolean ownAgency;
        private Boolean aceExport;

        public Agency() {}

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Country getCountry() { return country; }
        public void setCountry(Country country) { this.country = country; }
        public Address getAddress() { return address; }
        public void setAddress(Address address) { this.address = address; }
        public Boolean getOwnAgency() { return ownAgency; }
        public void setOwnAgency(Boolean ownAgency) { this.ownAgency = ownAgency; }
        public Boolean getAceExport() { return aceExport; }
        public void setAceExport(Boolean aceExport) { this.aceExport = aceExport; }
    }

    // Classe statique AgencyUser
    public static class AgencyUser {
        private Office office;
        private Operator operator;
        private Market market;
        private Agency agency;
        private String name;
        private String code;

        public AgencyUser() {}

        public Office getOffice() { return office; }
        public void setOffice(Office office) { this.office = office; }
        public Operator getOperator() { return operator; }
        public void setOperator(Operator operator) { this.operator = operator; }
        public Market getMarket() { return market; }
        public void setMarket(Market market) { this.market = market; }
        public Agency getAgency() { return agency; }
        public void setAgency(Agency agency) { this.agency = agency; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
    }

    // Classe statique Office
    public static class Office {
        private String code;
        private String name;

        public Office() {}

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    // Classe statique Commission
    public static class Commission {
        private Integer percent;
        private Double amount;
        private String currency;

        public Commission() {}

        public Integer getPercent() { return percent; }
        public void setPercent(Integer percent) { this.percent = percent; }
        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }

    // Classe statique Document
    public static class Document {
        private Integer documentType;
        private String url;
        private String name;
        private Boolean isDefault;
        private Boolean proforma;
        private Integer fromToType;

        public Document() {}

        public Integer getDocumentType() { return documentType; }
        public void setDocumentType(Integer documentType) { this.documentType = documentType; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Boolean getIsDefault() { return isDefault; }
        public void setIsDefault(Boolean isDefault) { this.isDefault = isDefault; }
        public Boolean getProforma() { return proforma; }
        public void setProforma(Boolean proforma) { this.proforma = proforma; }
        public Integer getFromToType() { return fromToType; }
        public void setFromToType(Integer fromToType) { this.fromToType = fromToType; }
    }

    // Classe statique ReservableInfo
    public static class ReservableInfo {
        private Boolean reservable;

        public ReservableInfo() {}

        public Boolean getReservable() { return reservable; }
        public void setReservable(Boolean reservable) { this.reservable = reservable; }
    }

    // Classe statique Location
    public static class Location {
        private String code;
        private String internationalCode;
        private String name;
        private Integer type;
        private String latitude;
        private String longitude;
        private String parentId;
        private String countryId;
        private Integer provider;
        private Boolean isTopRegion;
        private String id;

        public Location() {}

        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getInternationalCode() { return internationalCode; }
        public void setInternationalCode(String internationalCode) { this.internationalCode = internationalCode; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getLatitude() { return latitude; }
        public void setLatitude(String latitude) { this.latitude = latitude; }
        public String getLongitude() { return longitude; }
        public void setLongitude(String longitude) { this.longitude = longitude; }
        public String getParentId() { return parentId; }
        public void setParentId(String parentId) { this.parentId = parentId; }
        public String getCountryId() { return countryId; }
        public void setCountryId(String countryId) { this.countryId = countryId; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
        public Boolean getIsTopRegion() { return isTopRegion; }
        public void setIsTopRegion(Boolean isTopRegion) { this.isTopRegion = isTopRegion; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
    }

    // Classe statique ServiceDetails
    public static class ServiceDetails {
        private String serviceId;
        private HotelDetail hotelDetail;
        private Integer night;
        private String room;
        private String board;
        private String accom;
        private GeoLocation geoLocation;

        public ServiceDetails() {}

        public String getServiceId() { return serviceId; }
        public void setServiceId(String serviceId) { this.serviceId = serviceId; }
        public HotelDetail getHotelDetail() { return hotelDetail; }
        public void setHotelDetail(HotelDetail hotelDetail) { this.hotelDetail = hotelDetail; }
        public Integer getNight() { return night; }
        public void setNight(Integer night) { this.night = night; }
        public String getRoom() { return room; }
        public void setRoom(String room) { this.room = room; }
        public String getBoard() { return board; }
        public void setBoard(String board) { this.board = board; }
        public String getAccom() { return accom; }
        public void setAccom(String accom) { this.accom = accom; }
        public GeoLocation getGeoLocation() { return geoLocation; }
        public void setGeoLocation(GeoLocation geoLocation) { this.geoLocation = geoLocation; }
    }

    // Classe statique HotelDetail
    public static class HotelDetail {
        private Address address;
        private Location transferLocation;
        private Integer stopSaleGuaranteed;
        private Integer stopSaleStandart;
        private GeoLocation geolocation;
        private Location location;
        private Country country;
        private City city;
        private String id;
        private String name;

        public HotelDetail() {}

        public Address getAddress() { return address; }
        public void setAddress(Address address) { this.address = address; }
        public Location getTransferLocation() { return transferLocation; }
        public void setTransferLocation(Location transferLocation) { this.transferLocation = transferLocation; }
        public Integer getStopSaleGuaranteed() { return stopSaleGuaranteed; }
        public void setStopSaleGuaranteed(Integer stopSaleGuaranteed) { this.stopSaleGuaranteed = stopSaleGuaranteed; }
        public Integer getStopSaleStandart() { return stopSaleStandart; }
        public void setStopSaleStandart(Integer stopSaleStandart) { this.stopSaleStandart = stopSaleStandart; }
        public GeoLocation getGeolocation() { return geolocation; }
        public void setGeolocation(GeoLocation geolocation) { this.geolocation = geolocation; }
        public Location getLocation() { return location; }
        public void setLocation(Location location) { this.location = location; }
        public Country getCountry() { return country; }
        public void setCountry(Country country) { this.country = country; }
        public City getCity() { return city; }
        public void setCity(City city) { this.city = city; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    // Classe statique GeoLocation
    public static class GeoLocation {
        private String longitude;
        private String latitude;

        public GeoLocation() {}

        public String getLongitude() { return longitude; }
        public void setLongitude(String longitude) { this.longitude = longitude; }
        public String getLatitude() { return latitude; }
        public void setLatitude(String latitude) { this.latitude = latitude; }
    }

    // Classe statique CancellationPolicy
    public static class CancellationPolicy {
        private String dueDate;
        private Price price;
        private Integer provider;

        public CancellationPolicy() {}

        public String getDueDate() { return dueDate; }
        public void setDueDate(String dueDate) { this.dueDate = dueDate; }
        public Price getPrice() { return price; }
        public void setPrice(Price price) { this.price = price; }
        public Integer getProvider() { return provider; }
        public void setProvider(Integer provider) { this.provider = provider; }
    }

    // Classe statique PaymentDetail
    public static class PaymentDetail {
        private List<PaymentPlan> paymentPlan;

        public PaymentDetail() {}

        public List<PaymentPlan> getPaymentPlan() { return paymentPlan; }
        public void setPaymentPlan(List<PaymentPlan> paymentPlan) { this.paymentPlan = paymentPlan; }
    }

    // Classe statique PaymentPlan
    public static class PaymentPlan {
        private Integer paymentNo;
        private String dueDate;
        private PriceWithPercent price;
        private Boolean paymentStatus;

        public PaymentPlan() {}

        public Integer getPaymentNo() { return paymentNo; }
        public void setPaymentNo(Integer paymentNo) { this.paymentNo = paymentNo; }
        public String getDueDate() { return dueDate; }
        public void setDueDate(String dueDate) { this.dueDate = dueDate; }
        public PriceWithPercent getPrice() { return price; }
        public void setPrice(PriceWithPercent price) { this.price = price; }
        public Boolean getPaymentStatus() { return paymentStatus; }
        public void setPaymentStatus(Boolean paymentStatus) { this.paymentStatus = paymentStatus; }
    }

    // Classe statique PriceWithPercent
    public static class PriceWithPercent {
        private Integer percent;
        private Double amount;
        private String currency;

        public PriceWithPercent() {}

        public Integer getPercent() { return percent; }
        public void setPercent(Integer percent) { this.percent = percent; }
        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }
}