package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paximum.demo.models.BeginTransactionResponse.Body;
import com.paximum.demo.models.BeginTransactionResponse.Header;
import com.paximum.demo.models.BeginTransactionResponse.Message;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
	 private BeginTransactionResponse.Header header1;

	 
	 private BeginTransactionResponse.Body body1;

 
    public BeginTransactionResponse.Header getHeader1() {
        return header1;
    }


    public BeginTransactionResponse.Body getBody1() {
        return body1;
    }




   

}