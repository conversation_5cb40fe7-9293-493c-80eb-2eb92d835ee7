package com.paximum.demo.services;

import org.springframework.stereotype.Service;
import com.paximum.demo.config.BookingClient;
import com.paximum.demo.models.*;
import reactor.core.publisher.Mono;
import java.util.Collections;

@Service
public class BookingService {

    private final BookingClient bookingClient;

    public BookingService(BookingClient bookingClient) {
        this.bookingClient = bookingClient;
    }
    

    














    
    public Mono<BeginTransactionResponse> beginTransaction(BeginTransactionRequest request, String token) {
        return bookingClient.beginTransaction(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Erreur lors du début de la transaction", e)));
    }
    
    public Mono<AddServicesResponse> addServices(AddServicesRequest request, String token) {
        return bookingClient.addServices(request, token)
            .onErrorResume(e -> {
                if (e instanceof NullPointerException && e.getMessage() != null && 
                    e.getMessage().contains("EncodedOfferIdModel.getSearchId()")) {
                    return Mono.error(new RuntimeException("Invalid offer ID format. The searchId is missing in the encoded offer.", e));
                }
                return Mono.error(new RuntimeException("Error adding services: " + e.getMessage(), e));
            });
    }
    
    public Mono<RemoveServicesResponse> removeServices(RemoveServicesRequest request, String token) {
        return bookingClient.removeServices(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error removing services", e)));
    }
    
    public Mono<SetReservationInfoResponse> setReservationInfo(SetReservationInfoRequest request, String token) {
        // Vérification préalable des données de passeport
        if (request.getTravellers() != null) {
            for (var traveller : request.getTravellers()) {
                if (traveller.getPassportInfo() != null && traveller.getPassportInfo().getIssueCountryCode() == null) {
                    // Si le pays d'émission est absent, utiliser le code de nationalité comme valeur par défaut
                    if (traveller.getNationality() != null && traveller.getNationality().getTwoLetterCode() != null) {
                        String countryCode = traveller.getNationality().getTwoLetterCode();
                        traveller.getPassportInfo().setIssueCountryCode(countryCode);
                        System.out.println("Attention: issueCountryCode manquant pour le voyageur " + 
                                          traveller.getName() + " " + traveller.getSurname() + 
                                          ", utilisation de la nationalité (" + countryCode + ") par défaut.");
                    }
                }
            }
        }
        
        return bookingClient.setReservationInfo(request, token)
            .onErrorResume(e -> {
                String errorMessage = e.getMessage() != null ? e.getMessage() : "";
                
                // Gestion spécifique des erreurs liées au passeport
                if (errorMessage.contains("passport") || errorMessage.contains("issueCountryCode")) {
                    return Mono.error(new RuntimeException(
                        "Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, " +
                        "notamment le champ 'issueCountryCode'. Message original: " + errorMessage, e));
                }
                
                return Mono.error(new RuntimeException("Erreur lors de la définition des informations de réservation: " + errorMessage, e));
            });
    }

    public Mono<CommitTransactionResponse> commitTransaction(CommitTransactionRequest request, String token) {
        return verifyPaymentStatus(request.getTransactionId(), token)
            .flatMap(isPaid -> {
                if (!isPaid) {
                    return Mono.error(new RuntimeException("Payment not received. Cannot commit transaction."));
                }
                return bookingClient.commitTransaction(request, token);
            })
            .onErrorResume(e -> {
                String errorMessage = e.getMessage() != null ? e.getMessage() : "";
                
                // Gestion spécifique des erreurs de validation du passeport
                if (errorMessage.contains("passport issue country") || 
                    errorMessage.contains("For passenger") && errorMessage.contains("is empty or invalid")) {
                    return Mono.error(new RuntimeException(
                        "Erreur de validation du passeport: Le pays d'émission du passeport (issueCountryCode) est manquant ou invalide. " +
                        "Veuillez vérifier que le champ 'issueCountryCode' est présent dans les informations de passeport. " +
                        "Message original: " + errorMessage));
                }
                
                return Mono.error(new RuntimeException("Error committing transaction: " + errorMessage, e));
            });
    }

    private Mono<Boolean> verifyPaymentStatus(String transactionId, String token) {
        // Pour les tests, on retourne toujours true (paiement accepté)
        // TODO: Remettre la vérification réelle en production
        return Mono.just(true);
        
        /*
        GetReservationDetailRequest detailRequest = new GetReservationDetailRequest();
        detailRequest.setReservationNumber(transactionId);
        
        return bookingClient.getReservationDetail(detailRequest, token)
            .map(response -> {
                if (response.getBody() != null && 
                    response.getBody().getReservationData() != null && 
                    response.getBody().getReservationData().getReservationInfo() != null) {
                    
                    Integer paymentStatus = response.getBody().getReservationData().getReservationInfo().getPaymentStatus();
                    return paymentStatus != null && paymentStatus == 2;
                }
                return false;
            })
            .onErrorResume(e -> {
                return Mono.error(new RuntimeException("Error verifying payment status: " + e.getMessage(), e));
            });
        */
    }
    
    public Mono<GetReservationDetailResponse> getReservationDetail(GetReservationDetailRequest request, String token) {
        return bookingClient.getReservationDetail(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error fetching reservation details", e)));
    }

    public Mono<GetReservationListResponse> getReservationList(GetReservationListRequest request, String token) {
        return bookingClient.getReservationList(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error fetching reservation list", e)));
    }

    public Mono<GetPaymentListResponse> getPaymentList(GetPaymentListRequest request, String token) {
        return bookingClient.getPaymentList(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error retrieving payment list", e)));
    }

    public Mono<GetCancellationPenaltyResponse> getCancellationPenalty(GetCancellationPenaltyRequest request, String token) {
        return bookingClient.getCancellationPenalty(request, token)
            .onErrorResume(e -> Mono.error(new RuntimeException("Error getting cancellation penalty: " + e.getMessage(), e)));
    }

    public Mono<CancelReservationResponse> cancelReservation(CancelReservationRequest request, String token) {
        return getCancellationPenalty(new GetCancellationPenaltyRequest(request.getReservationNumber()), token)
            .flatMap(penaltyResponse -> {
                // Vérifier si la réponse et ses données sont valides
                if (penaltyResponse == null || penaltyResponse.getBody() == null) {
                    return Mono.error(new RuntimeException("Invalid cancellation penalty response"));
                }
                
                // Récupérer la liste des pénalités ou une liste vide si elle est null
                java.util.List<GetCancellationPenaltyResponse.CancelPenalty> penalties = 
                    penaltyResponse.getBody().getCancelPenalties() != null 
                        ? penaltyResponse.getBody().getCancelPenalties() 
                        : Collections.emptyList();
                
                if (penalties.isEmpty()) {
                    return Mono.error(new RuntimeException("No cancellation penalties available for this reservation"));
                }
                
                // Vérifier si l'annulation est possible avec la raison spécifiée
                boolean isCancelable = penalties.stream()
                    .filter(penalty -> penalty.getReason() != null && penalty.getReason().getId() != null)
                    .anyMatch(penalty -> {
                        boolean isMatchingReason = penalty.getReason().getId().equals(request.getReason());
                        boolean canCancel = penalty.getIsCancelable() != null && penalty.getIsCancelable();
                        return isMatchingReason && canCancel;
                    });
                
                if (!isCancelable) {
                    return Mono.error(new RuntimeException("Reservation cannot be cancelled with the provided reason: " + request.getReason()));
                }
                
                // Procéder à l'annulation
                return bookingClient.cancelReservation(request, token);
            })
            .onErrorResume(e -> Mono.error(new RuntimeException("Error cancelling reservation: " + e.getMessage(), e)));
    }
}
