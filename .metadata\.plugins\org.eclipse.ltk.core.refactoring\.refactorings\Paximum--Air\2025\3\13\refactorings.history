<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.controllers.priceMethodeController&apos; to &apos;productserviceController&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.controllers.priceMethodeController&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.controllers.productserviceController&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;priceMethodeController&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.controllers{priceMethodeController.java[priceMethodeController" matchStrategy="1" name="productserviceController" qualified="false" references="true" similarDeclarations="false" stamp="1742920649441" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.controllers.productserviceController&apos; to &apos;productController&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.controllers.productserviceController&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.controllers.productController&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;productserviceController&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.controllers{productserviceController.java[productserviceController" matchStrategy="1" name="productController" qualified="false" references="true" similarDeclarations="false" stamp="1742920686097" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.config.priceMethodeClient&apos; to &apos;productClient&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.config.priceMethodeClient&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.config.productClient&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;priceMethodeClient&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.config{priceMethodeClient.java[priceMethodeClient" matchStrategy="1" name="productClient" qualified="false" references="true" similarDeclarations="false" stamp="1742920715634" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.paximum.demo.services.priceMethodeService&apos; to &apos;productService&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.demo.services.priceMethodeService&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.demo.services.productService&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;priceMethodeService&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.demo.services{priceMethodeService.java[priceMethodeService" matchStrategy="1" name="productService" qualified="false" references="true" similarDeclarations="false" stamp="1742920756939" textual="false" version="1.0"/>
</session>