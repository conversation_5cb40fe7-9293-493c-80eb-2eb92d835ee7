package com.paximum.demo.config;

import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import com.paximum.demo.models.ApiResponsePService;
import com.paximum.demo.models.PriceSearchRequest;
import com.paximum.demo.models.GetOffersRequest;
import com.paximum.demo.models.GetOffersResponse;

import reactor.core.publisher.Mono;

@Component
public class ProductClient {

    private final WebClient webClient;

    public ProductClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
            .baseUrl("http://service.stage.paximum.com/v2")
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024))
            .build();
    }

    // Price search method
    public Mono<ApiResponsePService> searchPrice(PriceSearchRequest request, String token) {
        return this.webClient.post()
                .uri("/api/productservice/pricesearch")
                .header("Authorization", "Bearer " + token)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ApiResponsePService.class);
    }

    // Get offers method
    public Mono<GetOffersResponse> getOffers(GetOffersRequest request, String token) {
        return this.webClient.post()
                .uri("/api/productservice/getoffers")
                .header("Authorization", "Bearer " + token)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(GetOffersResponse.class);
    }
}