package com.paximum.demo.config;

import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import com.paximum.demo.models.AddServicesRequest;
import com.paximum.demo.models.AddServicesResponse;
import com.paximum.demo.models.BeginTransactionRequest;
import com.paximum.demo.models.BeginTransactionResponse;
import com.paximum.demo.models.BookingTransactionRequest;
import com.paximum.demo.models.BookingTransactionResponse;
import com.paximum.demo.models.CancelReservationRequest;
import com.paximum.demo.models.CancelReservationResponse;
import com.paximum.demo.models.CommitTransactionRequest;
import com.paximum.demo.models.CommitTransactionResponse;
import com.paximum.demo.models.GetCancellationPenaltyRequest;
import com.paximum.demo.models.GetCancellationPenaltyResponse;
import com.paximum.demo.models.GetPaymentListRequest;
import com.paximum.demo.models.GetPaymentListResponse;
import com.paximum.demo.models.GetReservationDetailRequest;
import com.paximum.demo.models.GetReservationDetailResponse;
import com.paximum.demo.models.GetReservationListRequest;
import com.paximum.demo.models.GetReservationListResponse;
import com.paximum.demo.models.RemoveServicesRequest;
import com.paximum.demo.models.RemoveServicesResponse;
import com.paximum.demo.models.SetReservationInfoRequest;
import com.paximum.demo.models.SetReservationInfoResponse;


import reactor.core.publisher.Mono;

@Component
public class BookingClient {

    private final WebClient webClient;

    public BookingClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl("http://service.stage.paximum.com/v2/api/bookingservice").build();
    }
 // Méthode pour Bookingtransaction
    public Mono<BookingTransactionResponse>bookingTransaction(BookingTransactionRequest request, String token) {
        return this.webClient.post()
            .uri("/bookingtransaction")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(BeginTransactionResponse.class);
    }


    // Méthode pour BeginTransactionResponse
    public Mono<BeginTransactionResponse> beginTransaction(BeginTransactionRequest request, String token) {
        return this.webClient.post()
            .uri("/begintransaction")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(BeginTransactionResponse.class);
    }
    
 // Méthode pour addservices
    public Mono<AddServicesResponse> addServices(AddServicesRequest request, String token) {
        return this.webClient.post()
            .uri("/addservices")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .onStatus(
                status -> status.is4xxClientError() || status.is5xxServerError(),
                response -> response.bodyToMono(String.class)
                    .flatMap(body -> {
                        if (body.contains("EncodedOfferIdModel.getSearchId()")) {
                            return Mono.error(new RuntimeException("Invalid offer ID format. The searchId is missing in the encoded offer."));
                        }
                        return Mono.error(new RuntimeException("Error from service: " + body));
                    })
            )
            .bodyToMono(AddServicesResponse.class);
    }
    
 // Méthode pour RemoveServices
    public Mono<RemoveServicesResponse> removeServices(RemoveServicesRequest request, String token) {
        return this.webClient.post()
            .uri("/removeservices")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(RemoveServicesResponse.class);
    }
    
 // Méthode pour setreservationinfo
    public Mono<SetReservationInfoResponse> setReservationInfo(SetReservationInfoRequest request, String token) {
        System.out.println(request.toString());
        return this.webClient.post()
            .uri("/setreservationinfo")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(SetReservationInfoResponse.class);
    }
    // Méthode pour CommitTransaction

    public Mono<CommitTransactionResponse> commitTransaction(CommitTransactionRequest request, String token) {
        return this.webClient.post()
            .uri("/committransaction")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(CommitTransactionResponse.class);
    }
    
 // Méthode pour GetReservationDetail
    public Mono<GetReservationDetailResponse> getReservationDetail(GetReservationDetailRequest request, String token) {
        return this.webClient.post()
            .uri("/getreservationdetail")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(GetReservationDetailResponse.class);
    }
 // Méthode pour GetReservationList
    
    public Mono<GetReservationListResponse> getReservationList(GetReservationListRequest request, String token) {
        return this.webClient.post()
            .uri("/getreservationlist")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(GetReservationListResponse.class);
    }
    // Méthode pour GetPaymentList
    public Mono<GetPaymentListResponse> getPaymentList(GetPaymentListRequest request, String token) {
        return this.webClient.post()
            .uri("/getpaymentlist")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(GetPaymentListResponse.class);
    }

    public Mono<GetCancellationPenaltyResponse> getCancellationPenalty(GetCancellationPenaltyRequest request, String token) {
        return this.webClient.post()
            .uri("/getcancellationpenalty")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(GetCancellationPenaltyResponse.class);
    }

    public Mono<CancelReservationResponse> cancelReservation(CancelReservationRequest request, String token) {
        return this.webClient.post()
            .uri("/cancelreservation")
            .header("Authorization", "Bearer " + token)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(CancelReservationResponse.class);
    }
}