eclipse.preferences.version=1
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.cloud-eureka=1
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.cloud-feign=1
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.devtools=5
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.lombok=1
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.security=1
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.web=4
org.springframework.ide.eclipse.boot.wizard.PopularityTracker.webflux=1
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.artifactId=Paximum--Air
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.bootVersion=3.4.3
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.description=e-tourism
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.groupId=com.paximum
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.javaVersion=17
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.language=java
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.name=Paximum--Air
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.packageName=com.paximum.auth
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.packaging=jar
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.type=MAVEN
org.springframework.ide.eclipse.boot.wizard.PreferredSelections.version=0.0.1-SNAPSHOT
org.springframework.ide.eclipse.boot.wizard.initializr.url=https\://start.spring.io\n
