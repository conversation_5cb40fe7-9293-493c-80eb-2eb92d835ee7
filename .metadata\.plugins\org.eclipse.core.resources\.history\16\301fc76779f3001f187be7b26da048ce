package com.e_tourism.auth.service;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class TourvisioAuthService {

    private final RestTemplate restTemplate;

    public TourvisioAuthService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public String authenticate() {
        String url = "http://service.stage.paximum.com/v2/api/authenticationservice/login";

        // Création du payload
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("Agency", "TON_AGENCE");
        requestBody.put("User", "TON_USER");
        requestBody.put("Password", "TON_PASSWORD");
        requestBody.put("Culture", "fr-FR");

        // Headers
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");

        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        // Envoi de la requête POST
        ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Map.class);

        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            return (String) response.getBody().get("token");
        } else {
            throw new RuntimeException("Échec de l'authentification avec Tourvisio");
        }
    }
}
