package com.example.demo.service;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.example.demo.dto.AuthRequest;
import com.example.demo.dto.AuthResponse;

import reactor.core.publisher.Mono;

@Service
public class AuthService {

    private final WebClient webClient;

    public AuthService(WebClient webClient) {
        this.webClient = webClient;
    }

    public Mono<String> authenticate(String agency, String user, String password) {
        AuthRequest authRequest = new AuthRequest(agency, user, password);

        return webClient.post()
                .uri("/api/authenticationservice/login")
                .bodyValue(authRequest)
                .retrieve()
                .bodyToMono(AuthResponse.class)
                .map(authResponse -> authResponse.getBody().getToken());
    }
}
