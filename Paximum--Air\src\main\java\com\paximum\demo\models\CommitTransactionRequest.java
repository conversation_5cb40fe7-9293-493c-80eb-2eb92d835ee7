package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommitTransactionRequest {
    private String transactionId;
    private Integer paymentOption;
    private PaymentInformation paymentInformation;
    private VCCInformation vccInformation;

    public CommitTransactionRequest() {}

    public String getTransactionId() { return transactionId; }
    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
    public Integer getPaymentOption() { return paymentOption; }
    public void setPaymentOption(Integer paymentOption) { this.paymentOption = paymentOption; }
    public PaymentInformation getPaymentInformation() { return paymentInformation; }
    public void setPaymentInformation(PaymentInformation paymentInformation) { this.paymentInformation = paymentInformation; }
    public VCCInformation getVccInformation() { return vccInformation; }
    public void setVccInformation(VCCInformation vccInformation) { this.vccInformation = vccInformation; }

    public static class PaymentInformation {
        private String accountName;
        private Integer paymentTypeId;
        private Price paymentPrice;
        private String installmentCount;
        private String paymentDate;
        private String receiptType;
        private String reference;
        private String paymentToken;

        public PaymentInformation() {}

        public String getAccountName() { return accountName; }
        public void setAccountName(String accountName) { this.accountName = accountName; }
        public Integer getPaymentTypeId() { return paymentTypeId; }
        public void setPaymentTypeId(Integer paymentTypeId) { this.paymentTypeId = paymentTypeId; }
        public Price getPaymentPrice() { return paymentPrice; }
        public void setPaymentPrice(Price paymentPrice) { this.paymentPrice = paymentPrice; }
        public String getInstallmentCount() { return installmentCount; }
        public void setInstallmentCount(String installmentCount) { this.installmentCount = installmentCount; }
        public String getPaymentDate() { return paymentDate; }
        public void setPaymentDate(String paymentDate) { this.paymentDate = paymentDate; }
        public String getReceiptType() { return receiptType; }
        public void setReceiptType(String receiptType) { this.receiptType = receiptType; }
        public String getReference() { return reference; }
        public void setReference(String reference) { this.reference = reference; }
        public String getPaymentToken() { return paymentToken; }
        public void setPaymentToken(String paymentToken) { this.paymentToken = paymentToken; }
    }

    public static class VCCInformation {
        private String vccNo;
        private String vccExpDate;
        private String vccSecNo;
        private String vccHolderName;

        public VCCInformation() {}

        public String getVccNo() { return vccNo; }
        public void setVccNo(String vccNo) { this.vccNo = vccNo; }
        public String getVccExpDate() { return vccExpDate; }
        public void setVccExpDate(String vccExpDate) { this.vccExpDate = vccExpDate; }
        public String getVccSecNo() { return vccSecNo; }
        public void setVccSecNo(String vccSecNo) { this.vccSecNo = vccSecNo; }
        public String getVccHolderName() { return vccHolderName; }
        public void setVccHolderName(String vccHolderName) { this.vccHolderName = vccHolderName; }
    }

    public static class Price {
        private Double amount;
        private String currency;

        public Price() {}

        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }
}