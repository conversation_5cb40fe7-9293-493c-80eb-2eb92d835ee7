<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;product-service&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741001915700" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;product-service&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741001988408" subPackages="false" version="1.0"/>&#x0A;&#x0A;<refactoring comment="Rename package &apos;com.example.prod.config&apos; to &apos;com.paximum.productservice.config&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.example.prod.config&apos;&#x0D;&#x0A;- Renamed element: &apos;product-service/src/main/java/com.paximum.productservice.config&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.example.prod.config&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.prod.config" name="com.paximum.productservice.config" qualified="false" references="true" stamp="1741002147339" textual="false" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.productservice.config&apos; to &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum.productservice.config&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.config" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741002212086" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;product-service&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.productservice.config.priceMethodeClient.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.config{priceMethodeClient.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741002798187" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.productservice&apos; to &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum.productservice&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190240906" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.productservice.config&apos; to &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum.productservice.config&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.config" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190244781" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.productservice.controllers&apos; to &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum.productservice.controllers&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.controllers" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190246521" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.productservice.models&apos; to &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum.productservice.models&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.models" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190248452" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.productservice.services&apos; to &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;product-service&apos;&#x0D;&#x0A;- Destination element: &apos;product_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;product-service/src/main/java/com.paximum.productservice.services&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=product_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.productservice.services" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190250862" version="1.0"/>
</session>