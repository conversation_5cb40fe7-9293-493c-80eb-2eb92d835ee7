package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse {
    private Header header;
    private Body body;
    
    public ApiResponse() {}
    
    public ApiResponse(Body body, Header header) {
        this.body = body;
        this.header = header;
    }
    
    public Header getHeader() {
        return header;
    }
    
    public void setHeader(Header header) {
        this.header = header;
    }
    
    public Body getBody() {
        return body;
    }
    
    public void setBody(Body body) {
        this.body = body;
    }
}
