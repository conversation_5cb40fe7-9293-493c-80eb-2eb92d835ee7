<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;authentication-service&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth&apos;" description="Delete element" element1="src/main/java/com.paximum.auth" elements="0" flags="589830" id="org.eclipse.jdt.ui.delete" resources="1" stamp="1741002404536" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Rename compilation unit &apos;com.paximum.auth.models.AuthResponse.java&apos; to &apos;ApiResponse.java&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Original element: &apos;com.paximum.auth.models.AuthResponse.java&apos;&#x0D;&#x0A;- Renamed element: &apos;com.paximum.auth.models.ApiResponse.java&apos;" description="Rename compilation unit &apos;AuthResponse.java&apos;" flags="2" id="org.eclipse.jdt.ui.rename.compilationunit" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models{AuthResponse.java" name="ApiResponse.java" references="false" stamp="1741003154973" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;authentication-service&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Original element: &apos;static&apos;" description="Delete element" element1="src/main/resources/static" elements="0" flags="589830" id="org.eclipse.jdt.ui.delete" resources="1" stamp="1741003494833" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth&apos; to &apos;authentication_service/src/main/java/com.paximum.auth&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication_service/src/main/java/com.paximum.auth&apos;&#x0D;&#x0A;- Original element: &apos;authentication-service/src/main/java/com.paximum.auth&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=authentication_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190052220" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.config&apos; to &apos;authentication_service/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication_service/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Original element: &apos;authentication-service/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=authentication_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190056858" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.controllers&apos; to &apos;authentication_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;authentication-service/src/main/java/com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=authentication_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.controllers" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190060030" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.models&apos; to &apos;authentication_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;authentication-service/src/main/java/com.paximum.auth.models&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=authentication_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190063444" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.services&apos; to &apos;authentication_service/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;authentication-service&apos;&#x0D;&#x0A;- Destination element: &apos;authentication_service/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;authentication-service/src/main/java/com.paximum.auth.services&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=authentication_service/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.services" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741190066358" version="1.0"/>
</session>