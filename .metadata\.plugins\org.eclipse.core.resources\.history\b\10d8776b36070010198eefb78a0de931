package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

public class GetReservationDetailResponse {

    @JsonProperty("header")
    private Header header;

    @JsonProperty("body")
    private Body body;

    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }

    // Classes imbriquées

    public static class Header {
        @JsonProperty("requestId")
        private String requestId;

        @JsonProperty("success")
        private Boolean success;

        @JsonProperty("responseTime")
        private String responseTime;

        @JsonProperty("messages")
        private List<Message> messages;

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public Boolean getSuccess() {
            return success;
        }

        public void setSuccess(Boolean success) {
            this.success = success;
        }

        public String getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(String responseTime) {
            this.responseTime = responseTime;
        }

        public List<Message> getMessages() {
            return messages;
        }

        public void setMessages(List<Message> messages) {
            this.messages = messages;
        }
    }

    public static class Message {
        @JsonProperty("id")
        private Integer id;

        @JsonProperty("code")
        private String code;

        @JsonProperty("messageType")
        private Integer messageType;

        @JsonProperty("message")
        private String message;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public Integer getMessageType() {
            return messageType;
        }

        public void setMessageType(Integer messageType) {
            this.messageType = messageType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    public static class Body {
        @JsonProperty("reservationNumber")
        private String reservationNumber;

        @JsonProperty("encryptedReservationNumber")
        private String encryptedReservationNumber;

        @JsonProperty("transactionId")
        private String transactionId;

        @JsonProperty("reservationData")
        private ReservationData reservationData;

        @JsonProperty("status")
        private Integer status;

        public String getReservationNumber() {
            return reservationNumber;
        }

        public void setReservationNumber(String reservationNumber) {
            this.reservationNumber = reservationNumber;
        }

        public String getEncryptedReservationNumber() {
            return encryptedReservationNumber;
        }

        public void setEncryptedReservationNumber(String encryptedReservationNumber) {
            this.encryptedReservationNumber = encryptedReservationNumber;
        }

        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public ReservationData getReservationData() {
            return reservationData;
        }

        public void setReservationData(ReservationData reservationData) {
            this.reservationData = reservationData;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    public static class ReservationData {
        @JsonProperty("travellers")
        private List<Traveller> travellers;

        @JsonProperty("reservationInfo")
        private ReservationInfo reservationInfo;

        @JsonProperty("services")
        private List<Service> services;

        @JsonProperty("paymentDetail")
        private PaymentDetail paymentDetail;

        @JsonProperty("invoices")
        private List<Object> invoices;

        public List<Traveller> getTravellers() {
            return travellers;
        }

        public void setTravellers(List<Traveller> travellers) {
            this.travellers = travellers;
        }

        public ReservationInfo getReservationInfo() {
            return reservationInfo;
        }

        public void setReservationInfo(ReservationInfo reservationInfo) {
            this.reservationInfo = reservationInfo;
        }

        public List<Service> getServices() {
            return services;
        }

        public void setServices(List<Service> services) {
            this.services = services;
        }

        public PaymentDetail getPaymentDetail() {
            return paymentDetail;
        }

        public void setPaymentDetail(PaymentDetail paymentDetail) {
            this.paymentDetail = paymentDetail;
        }

        public List<Object> getInvoices() {
            return invoices;
        }

        public void setInvoices(List<Object> invoices) {
            this.invoices = invoices;
        }
    }

    public static class Traveller {
        @JsonProperty("travellerId")
        private String travellerId;

        @JsonProperty("type")
        private Integer type;

        @JsonProperty("title")
        private Integer title;

        @JsonProperty("academicTitle")
        private AcademicTitle academicTitle;

        @JsonProperty("name")
        private String name;

        @JsonProperty("surname")
        private String surname;

        @JsonProperty("isLeader")
        private Boolean isLeader;

        @JsonProperty("birthDate")
        private String birthDate;

        @JsonProperty("age")
        private Integer age;

        @JsonProperty("nationality")
        private Nationality nationality;

        @JsonProperty("identityNumber")
        private String identityNumber;

        @JsonProperty("passportInfo")
        private PassportInfo passportInfo;

        @JsonProperty("address")
        private Address address;

        @JsonProperty("destinationAddress")
        private Object destinationAddress;

        @JsonProperty("services")
        private List<TravellerService> services;

        @JsonProperty("gender")
        private Integer gender;

        @JsonProperty("orderNumber")
        private Integer orderNumber;

        @JsonProperty("requiredFields")
        private List<String> requiredFields;

        @JsonProperty("documents")
        private List<Object> documents;

        @JsonProperty("passengerType")
        private Integer passengerType;

        @JsonProperty("additionalFields")
        private Map<String, String> additionalFields;

        @JsonProperty("insertFields")
        private List<Object> insertFields;

        @JsonProperty("status")
        private Integer status;

        public String getTravellerId() {
            return travellerId;
        }

        public void setTravellerId(String travellerId) {
            this.travellerId = travellerId;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Integer getTitle() {
            return title;
        }

        public void setTitle(Integer title) {
            this.title = title;
        }

        public AcademicTitle getAcademicTitle() {
            return academicTitle;
        }

        public void setAcademicTitle(AcademicTitle academicTitle) {
            this.academicTitle = academicTitle;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSurname() {
            return surname;
        }

        public void setSurname(String surname) {
            this.surname = surname;
        }

        public Boolean getIsLeader() {
            return isLeader;
        }

        public void setIsLeader(Boolean isLeader) {
            this.isLeader = isLeader;
        }

        public String getBirthDate() {
            return birthDate;
        }

        public void setBirthDate(String birthDate) {
            this.birthDate = birthDate;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public Nationality getNationality() {
            return nationality;
        }

        public void setNationality(Nationality nationality) {
            this.nationality = nationality;
        }

        public String getIdentityNumber() {
            return identityNumber;
        }

        public void setIdentityNumber(String identityNumber) {
            this.identityNumber = identityNumber;
        }

        public PassportInfo getPassportInfo() {
            return passportInfo;
        }

        public void setPassportInfo(PassportInfo passportInfo) {
            this.passportInfo = passportInfo;
        }

        public Address getAddress() {
            return address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        public Object getDestinationAddress() {
            return destinationAddress;
        }

        public void setDestinationAddress(Object destinationAddress) {
            this.destinationAddress = destinationAddress;
        }

        public List<TravellerService> getServices() {
            return services;
        }

        public void setServices(List<TravellerService> services) {
            this.services = services;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public Integer getOrderNumber() {
            return orderNumber;
        }

        public void setOrderNumber(Integer orderNumber) {
            this.orderNumber = orderNumber;
        }

        public List<String> getRequiredFields() {
            return requiredFields;
        }

        public void setRequiredFields(List<String> requiredFields) {
            this.requiredFields = requiredFields;
        }

        public List<Object> getDocuments() {
            return documents;
        }

        public void setDocuments(List<Object> documents) {
            this.documents = documents;
        }

        public Integer getPassengerType() {
            return passengerType;
        }

        public void setPassengerType(Integer passengerType) {
            this.passengerType = passengerType;
        }

        public Map<String, String> getAdditionalFields() {
            return additionalFields;
        }

        public void setAdditionalFields(Map<String, String> additionalFields) {
            this.additionalFields = additionalFields;
        }

        public List<Object> getInsertFields() {
            return insertFields;
        }

        public void setInsertFields(List<Object> insertFields) {
            this.insertFields = insertFields;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    public static class AcademicTitle {
        @JsonProperty("id")
        private String id;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static class Nationality {
        @JsonProperty("name")
        private String name;

        @JsonProperty("twoLetterCode")
        private String twoLetterCode;

        @JsonProperty("threeLetterCode")
        private String threeLetterCode;

        @JsonProperty("numericCode")
        private String numericCode;

        @JsonProperty("isdCode")
        private String isdCode;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTwoLetterCode() {
            return twoLetterCode;
        }

        public void setTwoLetterCode(String twoLetterCode) {
            this.twoLetterCode = twoLetterCode;
        }

        public String getThreeLetterCode() {
            return threeLetterCode;
        }

        public void setThreeLetterCode(String threeLetterCode) {
            this.threeLetterCode = threeLetterCode;
        }

        public String getNumericCode() {
            return numericCode;
        }

        public void setNumericCode(String numericCode) {
            this.numericCode = numericCode;
        }

        public String getIsdCode() {
            return isdCode;
        }

        public void setIsdCode(String isdCode) {
            this.isdCode = isdCode;
        }
    }

    public static class PassportInfo {
        @JsonProperty("serial")
        private String serial;

        @JsonProperty("number")
        private String number;

        @JsonProperty("expireDate")
        private String expireDate;

        @JsonProperty("issueDate")
        private String issueDate;

        @JsonProperty("citizenshipCountryCode")
        private String citizenshipCountryCode;

        public String getSerial() {
            return serial;
        }

        public void setSerial(String serial) {
            this.serial = serial;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(String expireDate) {
            this.expireDate = expireDate;
        }

        public String getIssueDate() {
            return issueDate;
        }

        public void setIssueDate(String issueDate) {
            this.issueDate = issueDate;
        }

        public String getCitizenshipCountryCode() {
            return citizenshipCountryCode;
        }

        public void setCitizenshipCountryCode(String citizenshipCountryCode) {
            this.citizenshipCountryCode = citizenshipCountryCode;
        }
    }

    public static class Address {
        @JsonProperty("contactPhone")
        private ContactPhone contactPhone;

        @JsonProperty("email")
        private String email;

        @JsonProperty("address")
        private String address;

        @JsonProperty("zipCode")
        private String zipCode;

        @JsonProperty("city")
        private City city;

        @JsonProperty("country")
        private Country country;

        public ContactPhone getContactPhone() {
            return contactPhone;
        }

        public void setContactPhone(ContactPhone contactPhone) {
            this.contactPhone = contactPhone;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getZipCode() {
            return zipCode;
        }

        public void setZipCode(String zipCode) {
            this.zipCode = zipCode;
        }

        public City getCity() {
            return city;
        }

        public void setCity(City city) {
            this.city = city;
        }

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }
    }

    public static class ContactPhone {
        @JsonProperty("countryCode")
        private String countryCode;

        @JsonProperty("phoneNumber")
        private String phoneNumber;

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }
    }

    public static class City {
        @JsonProperty("id")
        private String id;

        @JsonProperty("name")
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Country {
        @JsonProperty("id")
        private String id;

        @JsonProperty("name")
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class TravellerService {
        @JsonProperty("id")
        private String id;

        @JsonProperty("type")
        private Integer type;

        @JsonProperty("ticketNo")
        private String ticketNo;

        @JsonProperty("price")
        private Price price;

        @JsonProperty("passengerType")
        private Integer passengerType;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getTicketNo() {
            return ticketNo;
        }

        public void setTicketNo(String ticketNo) {
            this.ticketNo = ticketNo;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public Integer getPassengerType() {
            return passengerType;
        }

        public void setPassengerType(Integer passengerType) {
            this.passengerType = passengerType;
        }
    }

    public static class Price {
        @JsonProperty("amount")
        private Double amount;

        @JsonProperty("currency")
        private String currency;

        public Double getAmount() {
            return amount;
        }

        public void setAmount(Double amount) {
            this.amount = amount;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    public static class ReservationInfo {
        @JsonProperty("bookingNumber")
        private String bookingNumber;

        @JsonProperty("encryptedBookingNumber")
        private String encryptedBookingNumber;

        @JsonProperty("market")
        private Market market;

        @JsonProperty("operator")
        private Operator operator;

        @JsonProperty("agency")
        private Agency agency;

        @JsonProperty("agencyUser")
        private AgencyUser agencyUser;

        @JsonProperty("beginDate")
        private String beginDate;

        @JsonProperty("endDate")
        private String endDate;

        @JsonProperty("note")
        private String note;

        @JsonProperty("agencyReservationNumber")
        private String agencyReservationNumber;

        @JsonProperty("salePrice")
        private Price salePrice;

        @JsonProperty("supplementDiscount")
        private Price supplementDiscount;

        @JsonProperty("passengerEB")
        private Price passengerEB;

        @JsonProperty("agencyEB")
        private Price agencyEB;

        @JsonProperty("passengerAmountToPay")
        private Price passengerAmountToPay;

        @JsonProperty("agencyAmountToPay")
        private Price agencyAmountToPay;

        @JsonProperty("discount")
        private Price discount;

        @JsonProperty("agencyBalance")
        private Price agencyBalance;

        @JsonProperty("passengerBalance")
        private Price passengerBalance;

        @JsonProperty("agencyCommission")
        private Commission agencyCommission;

        @JsonProperty("brokerCommission")
        private Commission brokerCommission;

        @JsonProperty("agencySupplementCommission")
        private Commission agencySupplementCommission;

        @JsonProperty("promotionAmount")
        private Price promotionAmount;

        @JsonProperty("priceToPay")
        private Price priceToPay;

        @JsonProperty("agencyPriceToPay")
        private Price agencyPriceToPay;

        @JsonProperty("passengerPriceToPay")
        private Price passengerPriceToPay;

        @JsonProperty("totalPrice")
        private Price totalPrice;

        @JsonProperty("reservationStatus")
        private Integer reservationStatus;

        @JsonProperty("confirmationStatus")
        private Integer confirmationStatus;

        @JsonProperty("paymentStatus")
        private Integer paymentStatus;

        @JsonProperty("documents")
        private List<Document> documents;

        @JsonProperty("otherDocuments")
        private List<Object> otherDocuments;

        @JsonProperty("reservableInfo")
        private ReservableInfo reservableInfo;

        @JsonProperty("paymentFrom")
        private Integer paymentFrom;

        @JsonProperty("departureCountry")
        private Location departureCountry;

        @JsonProperty("departureCity")
        private Location departureCity;

        @JsonProperty("arrivalCountry")
        private Location arrivalCountry;

        @JsonProperty("arrivalCity")
        private Location arrivalCity;

        @JsonProperty("createDate")
        private String createDate;

        @JsonProperty("changeDate")
        private String changeDate;

        @JsonProperty("additionalFields")
        private Map<String, String> additionalFields;

        @JsonProperty("additionalCode1")
        private String additionalCode1;

        @JsonProperty("additionalCode2")
        private String additionalCode2;

        @JsonProperty("additionalCode3")
        private String additionalCode3;

        @JsonProperty("additionalCode4")
        private String additionalCode4;

        @JsonProperty("agencyDiscount")
        private Double agencyDiscount;

        @JsonProperty("hasAvailablePromotionCode")
        private Boolean hasAvailablePromotionCode;

        public String getBookingNumber() {
            return bookingNumber;
        }

        public void setBookingNumber(String bookingNumber) {
            this.bookingNumber = bookingNumber;
        }

        public String getEncryptedBookingNumber() {
            return encryptedBookingNumber;
        }

        public void setEncryptedBookingNumber(String encryptedBookingNumber) {
            this.encryptedBookingNumber = encryptedBookingNumber;
        }

        public Market getMarket() {
            return market;
        }

        public void setMarket(Market market) {
            this.market = market;
        }

        public Operator getOperator() {
            return operator;
        }

        public void setOperator(Operator operator) {
            this.operator = operator;
        }

        public Agency getAgency() {
            return agency;
        }

        public void setAgency(Agency agency) {
            this.agency = agency;
        }

        public AgencyUser getAgencyUser() {
            return agencyUser;
        }

        public void setAgencyUser(AgencyUser agencyUser) {
            this.agencyUser = agencyUser;
        }

        public String getBeginDate() {
            return beginDate;
        }

        public void setBeginDate(String beginDate) {
            this.beginDate = beginDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }

        public String getAgencyReservationNumber() {
            return agencyReservationNumber;
        }

        public void setAgencyReservationNumber(String agencyReservationNumber) {
            this.agencyReservationNumber = agencyReservationNumber;
        }

        public Price getSalePrice() {
            return salePrice;
        }

        public void setSalePrice(Price salePrice) {
            this.salePrice = salePrice;
        }

        public Price getSupplementDiscount() {
            return supplementDiscount;
        }

        public void setSupplementDiscount(Price supplementDiscount) {
            this.supplementDiscount = supplementDiscount;
        }

        public Price getPassengerEB() {
            return passengerEB;
        }

        public void setPassengerEB(Price passengerEB) {
            this.passengerEB = passengerEB;
        }

        public Price getAgencyEB() {
            return agencyEB;
        }

        public void setAgencyEB(Price agencyEB) {
            this.agencyEB = agencyEB;
        }

        public Price getPassengerAmountToPay() {
            return passengerAmountToPay;
        }

        public void setPassengerAmountToPay(Price passengerAmountToPay) {
            this.passengerAmountToPay = passengerAmountToPay;
        }

        public Price getAgencyAmountToPay() {
            return agencyAmountToPay;
        }

        public void setAgencyAmountToPay(Price agencyAmountToPay) {
            this.agencyAmountToPay = agencyAmountToPay;
        }

        public Price getDiscount() {
            return discount;
        }

        public void setDiscount(Price discount) {
            this.discount = discount;
        }

        public Price getAgencyBalance() {
            return agencyBalance;
        }

        public void setAgencyBalance(Price agencyBalance) {
            this.agencyBalance = agencyBalance;
        }

        public Price getPassengerBalance() {
            return passengerBalance;
        }

        public void setPassengerBalance(Price passengerBalance) {
            this.passengerBalance = passengerBalance;
        }

        public Commission getAgencyCommission() {
            return agencyCommission;
        }

        public void setAgencyCommission(Commission agencyCommission) {
            this.agencyCommission = agencyCommission;
        }

        public Commission getBrokerCommission() {
            return brokerCommission;
        }

        public void setBrokerCommission(Commission brokerCommission) {
            this.brokerCommission = brokerCommission;
        }

        public Commission getAgencySupplementCommission() {
            return agencySupplementCommission;
        }

        public void setAgencySupplementCommission(Commission agencySupplementCommission) {
            this.agencySupplementCommission = agencySupplementCommission;
        }

        public Price getPromotionAmount() {
            return promotionAmount;
        }

        public void setPromotionAmount(Price promotionAmount) {
            this.promotionAmount = promotionAmount;
        }

        public Price getPriceToPay() {
            return priceToPay;
        }

        public void setPriceToPay(Price priceToPay) {
            this.priceToPay = priceToPay;
        }

        public Price getAgencyPriceToPay() {
            return agencyPriceToPay;
        }

        public void setAgencyPriceToPay(Price agencyPriceToPay) {
            this.agencyPriceToPay = agencyPriceToPay;
        }

        public Price getPassengerPriceToPay() {
            return passengerPriceToPay;
        }

        public void setPassengerPriceToPay(Price passengerPriceToPay) {
            this.passengerPriceToPay = passengerPriceToPay;
        }

        public Price getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(Price totalPrice) {
            this.totalPrice = totalPrice;
        }

        public Integer getReservationStatus() {
            return reservationStatus;
        }

        public void setReservationStatus(Integer reservationStatus) {
            this.reservationStatus = reservationStatus;
        }

        public Integer getConfirmationStatus() {
            return confirmationStatus;
        }

        public void setConfirmationStatus(Integer confirmationStatus) {
            this.confirmationStatus = confirmationStatus;
        }

        public Integer getPaymentStatus() {
            return paymentStatus;
        }

        public void setPaymentStatus(Integer paymentStatus) {
            this.paymentStatus = paymentStatus;
        }

        public List<Document> getDocuments() {
            return documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public List<Object> getOtherDocuments() {
            return otherDocuments;
        }

        public void setOtherDocuments(List<Object> otherDocuments) {
            this.otherDocuments = otherDocuments;
        }

        public ReservableInfo getReservableInfo() {
            return reservableInfo;
        }

        public void setReservableInfo(ReservableInfo reservableInfo) {
            this.reservableInfo = reservableInfo;
        }

        public Integer getPaymentFrom() {
            return paymentFrom;
        }

        public void setPaymentFrom(Integer paymentFrom) {
            this.paymentFrom = paymentFrom;
        }

        public Location getDepartureCountry() {
            return departureCountry;
        }

        public void setDepartureCountry(Location departureCountry) {
            this.departureCountry = departureCountry;
        }

        public Location getDepartureCity() {
            return departureCity;
        }

        public void setDepartureCity(Location departureCity) {
            this.departureCity = departureCity;
        }

        public Location getArrivalCountry() {
            return arrivalCountry;
        }

        public void setArrivalCountry(Location arrivalCountry) {
            this.arrivalCountry = arrivalCountry;
        }

        public Location getArrivalCity() {
            return arrivalCity;
        }

        public void setArrivalCity(Location arrivalCity) {
            this.arrivalCity = arrivalCity;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }

        public String getChangeDate() {
            return changeDate;
        }

        public void setChangeDate(String changeDate) {
            this.changeDate = changeDate;
        }

        public Map<String, String> getAdditionalFields() {
            return additionalFields;
        }

        public void setAdditionalFields(Map<String, String> additionalFields) {
            this.additionalFields = additionalFields;
        }

        public String getAdditionalCode1() {
            return additionalCode1;
        }

        public void setAdditionalCode1(String additionalCode1) {
            this.additionalCode1 = additionalCode1;
        }

        public String getAdditionalCode2() {
            return additionalCode2;
        }

        public void setAdditionalCode2(String additionalCode2) {
            this.additionalCode2 = additionalCode2;
        }

        public String getAdditionalCode3() {
            return additionalCode3;
        }

        public void setAdditionalCode3(String additionalCode3) {
            this.additionalCode3 = additionalCode3;
        }

        public String getAdditionalCode4() {
            return additionalCode4;
        }

        public void setAdditionalCode4(String additionalCode4) {
            this.additionalCode4 = additionalCode4;
        }

        public Double getAgencyDiscount() {
            return agencyDiscount;
        }

        public void setAgencyDiscount(Double agencyDiscount) {
            this.agencyDiscount = agencyDiscount;
        }

        public Boolean getHasAvailablePromotionCode() {
            return hasAvailablePromotionCode;
        }

        public void setHasAvailablePromotionCode(Boolean hasAvailablePromotionCode) {
            this.hasAvailablePromotionCode = hasAvailablePromotionCode;
        }
    }

    public static class Market {
        @JsonProperty("code")
        private String code;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class Operator {
        @JsonProperty("code")
        private String code;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class Agency {
        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        @JsonProperty("country")
        private Country country;

        @JsonProperty("address")
        private Address address;

        @JsonProperty("ownAgency")
        private Boolean ownAgency;

        @JsonProperty("aceExport")
        private Boolean aceExport;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }

        public Address getAddress() {
            return address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        public Boolean getOwnAgency() {
            return ownAgency;
        }

        public void setOwnAgency(Boolean ownAgency) {
            this.ownAgency = ownAgency;
        }

        public Boolean getAceExport() {
            return aceExport;
        }

        public void setAceExport(Boolean aceExport) {
            this.aceExport = aceExport;
        }
    }

    public static class AgencyUser {
        @JsonProperty("office")
        private Office office;

        @JsonProperty("operator")
        private Operator operator;

        @JsonProperty("market")
        private Market market;

        @JsonProperty("agency")
        private Agency agency;

        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;

        public Office getOffice() {
            return office;
        }

        public void setOffice(Office office) {
            this.office = office;
        }

        public Operator getOperator() {
            return operator;
        }

        public void setOperator(Operator operator) {
            this.operator = operator;
        }

        public Market getMarket() {
            return market;
        }

        public void setMarket(Market market) {
            this.market = market;
        }

        public Agency getAgency() {
            return agency;
        }

        public void setAgency(Agency agency) {
            this.agency = agency;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class Office {
        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Commission {
        @JsonProperty("percent")
        private Double percent;

        @JsonProperty("amount")
        private Double amount;

        @JsonProperty("currency")
        private String currency;

        public Double getPercent() {
            return percent;
        }

        public void setPercent(Double percent) {
            this.percent = percent;
        }

        public Double getAmount() {
            return amount;
        }

        public void setAmount(Double amount) {
            this.amount = amount;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    public static class Document {
        @JsonProperty("documentType")
        private Integer documentType;

        @JsonProperty("url")
        private String url;

        @JsonProperty("name")
        private String name;

        @JsonProperty("isDefault")
        private Boolean isDefault;

        @JsonProperty("proforma")
        private Boolean proforma;

        @JsonProperty("fromToType")
        private Integer fromToType;

        public Integer getDocumentType() {
            return documentType;
        }

        public void setDocumentType(Integer documentType) {
            this.documentType = documentType;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Boolean getIsDefault() {
            return isDefault;
        }

        public void setIsDefault(Boolean isDefault) {
            this.isDefault = isDefault;
        }

        public Boolean getProforma() {
            return proforma;
        }

        public void setProforma(Boolean proforma) {
            this.proforma = proforma;
        }

        public Integer getFromToType() {
            return fromToType;
        }

        public void setFromToType(Integer fromToType) {
            this.fromToType = fromToType;
        }
    }

    public static class ReservableInfo {
        @JsonProperty("reservable")
        private Boolean reservable;

        public Boolean getReservable() {
            return reservable;
        }

        public void setReservable(Boolean reservable) {
            this.reservable = reservable;
        }
    }

    public static class Location {
        @JsonProperty("code")
        private String code;

        @JsonProperty("internationalCode")
        private String internationalCode;

        @JsonProperty("name")
        private String name;

        @JsonProperty("type")
        private Integer type;

        @JsonProperty("latitude")
        private String latitude;

        @JsonProperty("longitude")
        private String longitude;

        @JsonProperty("parentId")
        private String parentId;

        @JsonProperty("countryId")
        private String countryId;

        @JsonProperty("provider")
        private Integer provider;

        @JsonProperty("isTopRegion")
        private Boolean isTopRegion;

        @JsonProperty("id")
        private String id;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getInternationalCode() {
            return internationalCode;
        }

        public void setInternationalCode(String internationalCode) {
            this.internationalCode = internationalCode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getCountryId() {
            return countryId;
        }

        public void setCountryId(String countryId) {
            this.countryId = countryId;
        }

        public Integer getProvider() {
            return provider;
        }

        public void setProvider(Integer provider) {
            this.provider = provider;
        }

        public Boolean getIsTopRegion() {
            return isTopRegion;
        }

        public void setIsTopRegion(Boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static class Service {
        @JsonProperty("orderNumber")
        private Integer orderNumber;

        @JsonProperty("note")
        private String note;

        @JsonProperty("departureCountry")
        private Location departureCountry;

        @JsonProperty("departureCity")
        private Location departureCity;

        @JsonProperty("arrivalCountry")
        private Location arrivalCountry;

        @JsonProperty("arrivalCity")
        private Location arrivalCity;

        @JsonProperty("serviceDetails")
        private ServiceDetails serviceDetails;

        @JsonProperty("isMainService")
        private Boolean isMainService;

        @JsonProperty("isRefundable")
        private Boolean isRefundable;

        @JsonProperty("bundle")
        private Boolean bundle;

        @JsonProperty("cancellationPolicies")
        private List<CancellationPolicy> cancellationPolicies;

        @JsonProperty("documents")
        private List<Document> documents;

        @JsonProperty("providerBookingID")
        private String providerBookingID;

        @JsonProperty("supplierBookingNumber")
        private String supplierBookingNumber;

        @JsonProperty("encryptedServiceNumber")
        private String encryptedServiceNumber;

        @JsonProperty("priceBreakDowns")
        private List<Object> priceBreakDowns;

        @JsonProperty("commission")
        private Double commission;

        @JsonProperty("reservableInfo")
        private ReservableInfo reservableInfo;

        @JsonProperty("unit")
        private Integer unit;

        @JsonProperty("confirmationStatus")
        private Integer confirmationStatus;

        @JsonProperty("serviceStatus")
        private Integer serviceStatus;

        @JsonProperty("productType")
        private Integer productType;

        @JsonProperty("createServiceTypeIfNotExists")
        private Boolean createServiceTypeIfNotExists;

        @JsonProperty("id")
        private String id;

        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        @JsonProperty("beginDate")
        private String beginDate;

        @JsonProperty("endDate")
        private String endDate;

        @JsonProperty("adult")
        private Integer adult;

        @JsonProperty("child")
        private Integer child;

        @JsonProperty("infant")
        private Integer infant;

        @JsonProperty("price")
        private Price price;

        @JsonProperty("includePackage")
        private Boolean includePackage;

        @JsonProperty("compulsory")
        private Boolean compulsory;

        @JsonProperty("isExtraService")
        private Boolean isExtraService;

        @JsonProperty("provider")
        private Integer provider;

        @JsonProperty("travellers")
        private List<String> travellers;

        @JsonProperty("thirdPartyRecord")
        private Boolean thirdPartyRecord;

        @JsonProperty("recordId")
        private Integer recordId;

        @JsonProperty("additionalFields")
        private Map<String, String> additionalFields;

        public Integer getOrderNumber() {
            return orderNumber;
        }

        public void setOrderNumber(Integer orderNumber) {
            this.orderNumber = orderNumber;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }

        public Location getDepartureCountry() {
            return departureCountry;
        }

        public void setDepartureCountry(Location departureCountry) {
            this.departureCountry = departureCountry;
        }

        public Location getDepartureCity() {
            return departureCity;
        }

        public void setDepartureCity(Location departureCity) {
            this.departureCity = departureCity;
        }

        public Location getArrivalCountry() {
            return arrivalCountry;
        }

        public void setArrivalCountry(Location arrivalCountry) {
            this.arrivalCountry = arrivalCountry;
        }

        public Location getArrivalCity() {
            return arrivalCity;
        }

        public void setArrivalCity(Location arrivalCity) {
            this.arrivalCity = arrivalCity;
        }

        public ServiceDetails getServiceDetails() {
            return serviceDetails;
        }

        public void setServiceDetails(ServiceDetails serviceDetails) {
            this.serviceDetails = serviceDetails;
        }

        public Boolean getIsMainService() {
            return isMainService;
        }

        public void setIsMainService(Boolean isMainService) {
            this.isMainService = isMainService;
        }

        public Boolean getIsRefundable() {
            return isRefundable;
        }

        public void setIsRefundable(Boolean isRefundable) {
            this.isRefundable = isRefundable;
        }

        public Boolean getBundle() {
            return bundle;
        }

        public void setBundle(Boolean bundle) {
            this.bundle = bundle;
        }

        public List<CancellationPolicy> getCancellationPolicies() {
            return cancellationPolicies;
        }

        public void setCancellationPolicies(List<CancellationPolicy> cancellationPolicies) {
            this.cancellationPolicies = cancellationPolicies;
        }

        public List<Document> getDocuments() {
            return documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public String getProviderBookingID() {
            return providerBookingID;
        }

        public void setProviderBookingID(String providerBookingID) {
            this.providerBookingID = providerBookingID;
        }

        public String getSupplierBookingNumber() {
            return supplierBookingNumber;
        }

        public void setSupplierBookingNumber(String supplierBookingNumber) {
            this.supplierBookingNumber = supplierBookingNumber;
        }

        public String getEncryptedServiceNumber() {
            return encryptedServiceNumber;
        }

        public void setEncryptedServiceNumber(String encryptedServiceNumber) {
            this.encryptedServiceNumber = encryptedServiceNumber;
        }

        public List<Object> getPriceBreakDowns() {
            return priceBreakDowns;
        }

        public void setPriceBreakDowns(List<Object> priceBreakDowns) {
            this.priceBreakDowns = priceBreakDowns;
        }

        public Double getCommission() {
            return commission;
        }

        public void setCommission(Double commission) {
            this.commission = commission;
        }

        public ReservableInfo getReservableInfo() {
            return reservableInfo;
        }

        public void setReservableInfo(ReservableInfo reservableInfo) {
            this.reservableInfo = reservableInfo;
        }

        public Integer getUnit() {
            return unit;
        }

        public void setUnit(Integer unit) {
            this.unit = unit;
        }

        public Integer getConfirmationStatus() {
            return confirmationStatus;
        }

        public void setConfirmationStatus(Integer confirmationStatus) {
            this.confirmationStatus = confirmationStatus;
        }

        public Integer getServiceStatus() {
            return serviceStatus;
        }

        public void setServiceStatus(Integer serviceStatus) {
            this.serviceStatus = serviceStatus;
        }

        public Integer getProductType() {
            return productType;
        }

        public void setProductType(Integer productType) {
            this.productType = productType;
        }

        public Boolean getCreateServiceTypeIfNotExists() {
            return createServiceTypeIfNotExists;
        }

        public void setCreateServiceTypeIfNotExists(Boolean createServiceTypeIfNotExists) {
            this.createServiceTypeIfNotExists = createServiceTypeIfNotExists;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getBeginDate() {
            return beginDate;
        }

        public void setBeginDate(String beginDate) {
            this.beginDate = beginDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public Integer getAdult() {
            return adult;
        }

        public void setAdult(Integer adult) {
            this.adult = adult;
        }

        public Integer getChild() {
            return child;
        }

        public void setChild(Integer child) {
            this.child = child;
        }

        public Integer getInfant() {
            return infant;
        }

        public void setInfant(Integer infant) {
            this.infant = infant;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
家的
        }

        public Boolean getIncludePackage() {
            return includePackage;
        }

        public void setIncludePackage(Boolean includePackage) {
            this.includePackage = includePackage;
        }

        public Boolean getCompulsory() {
            return compulsory;
        }

        public void setCompulsory(Boolean compulsory) {
            this.compulsory = compulsory;
        }

        public Boolean getIsExtraService() {
            return isExtraService;
        }

        public void setIsExtraService(Boolean isExtraService) {
            this.isExtraService = isExtraService;
        }

        public Integer getProvider() {
            return provider;
        }

        public void setProvider(Integer provider) {
            this.provider = provider;
        }

        public List<String> getTravellers() {
            return travellers;
        }

        public void setTravellers(List<String> travellers) {
            this.travellers = travellers;
        }

        public Boolean getThirdPartyRecord() {
            return thirdPartyRecord;
        }

        public void setThirdPartyRecord(Boolean thirdPartyRecord) {
            this.thirdPartyRecord = thirdPartyRecord;
        }

        public Integer getRecordId() {
            return recordId;
        }

        public void setRecordId(Integer recordId) {
            this.recordId = recordId;
        }

        public Map<String, String> getAdditionalFields() {
            return additionalFields;
        }

        public void setAdditionalFields(Map<String, String> additionalFields) {
            this.additionalFields = additionalFields;
        }
    }

    public static class ServiceDetails {
        @JsonProperty("serviceId")
        private String serviceId;

        @JsonProperty("hotelDetail")
        private HotelDetail hotelDetail;

        @JsonProperty("night")
        private Integer night;

        @JsonProperty("room")
        private String room;

        @JsonProperty("board")
        private String board;

        @JsonProperty("accom")
        private String accom;

        @JsonProperty("geoLocation")
        private GeoLocation geoLocation;

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        public HotelDetail getHotelDetail() {
            return hotelDetail;
        }

        public void setHotelDetail(HotelDetail hotelDetail) {
            this.hotelDetail = hotelDetail;
        }

        public Integer getNight() {
            return night;
        }

        public void setNight(Integer night) {
            this.night = night;
        }

        public String getRoom() {
            return room;
        }

        public void setRoom(String room) {
            this.room = room;
        }

        public String getBoard() {
            return board;
        }

        public void setBoard(String board) {
            this.board = board;
        }

        public String getAccom() {
            return accom;
        }

        public void setAccom(String accom) {
            this.accom = accom;
        }

        public GeoLocation getGeoLocation() {
            return geoLocation;
        }

        public void setGeoLocation(GeoLocation geoLocation) {
            this.geoLocation = geoLocation;
        }
    }

    public static class HotelDetail {
        @JsonProperty("address")
        private Address address;

        @JsonProperty("transferLocation")
        private Location transferLocation;

        @JsonProperty("stopSaleGuaranteed")
        private Integer stopSaleGuaranteed;

        @JsonProperty("stopSaleStandart")
        private Integer stopSaleStandart;

        @JsonProperty("geolocation")
        private GeoLocation geolocation;

        @JsonProperty("location")
        private Location location;

        @JsonProperty("country")
        private Country country;

        @JsonProperty("city")
        private City city;

        @JsonProperty("id")
        private String id;

        @JsonProperty("name")
        private String name;

        public Address getAddress() {
            return address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        public Location getTransferLocation() {
            return transferLocation;
        }

        public void setTransferLocation(Location transferLocation) {
            this.transferLocation = transferLocation;
        }

        public Integer getStopSaleGuaranteed() {
            return stopSaleGuaranteed;
        }

        public void setStopSaleGuaranteed(Integer stopSaleGuaranteed) {
            this.stopSaleGuaranteed = stopSaleGuaranteed;
        }

        public Integer getStopSaleStandart() {
            return stopSaleStandart;
        }

        public void setStopSaleStandart(Integer stopSaleStandart) {
            this.stopSaleStandart = stopSaleStandart;
        }

        public GeoLocation getGeolocation() {
            return geolocation;
        }

        public void setGeolocation(GeoLocation geolocation) {
            this.geolocation = geolocation;
        }

        public Location getLocation() {
            return location;
        }

        public void setLocation(Location location) {
            this.location = location;
        }

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }

        public City getCity() {
            return city;
        }

        public void setCity(City city) {
            this.city = city;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class GeoLocation {
        @JsonProperty("longitude")
        private String longitude;

        @JsonProperty("latitude")
        private String latitude;

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }
    }

    public static class CancellationPolicy {
        @JsonProperty("dueDate")
        private String dueDate;

        @JsonProperty("price")
        private Price price;

        @JsonProperty("provider")
        private Integer provider;

        public String getDueDate() {
            return dueDate;
        }

        public void setDueDate(String dueDate) {
            this.dueDate = dueDate;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public Integer getProvider() {
            return provider;
        }

        public void setProvider(Integer provider) {
            this.provider = provider;
        }
    }

    public static class PaymentDetail {
        @JsonProperty("paymentPlan")
        private List<PaymentPlan> paymentPlan;

        public List<PaymentPlan> getPaymentPlan() {
            return paymentPlan;
        }

        public void setPaymentPlan(List<PaymentPlan> paymentPlan) {
            this.paymentPlan = paymentPlan;
        }
    }

    public static class PaymentPlan {
        @JsonProperty("paymentNo")
        private Integer paymentNo;

        @JsonProperty("dueDate")
        private String dueDate;

        @JsonProperty("price")
        private Price price;

        @JsonProperty("paymentStatus")
        private Boolean paymentStatus;

        public Integer getPaymentNo() {
            return paymentNo;
        }

        public void setPaymentNo(Integer paymentNo) {
            this.paymentNo = paymentNo;
        }

        public String getDueDate() {
            return dueDate;
        }

        public void setDueDate(String dueDate) {
            this.dueDate = dueDate;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public Boolean getPaymentStatus() {
            return paymentStatus;
        }

        public void setPaymentStatus(Boolean paymentStatus) {
            this.paymentStatus = paymentStatus;
        }
    }
}