package com.example.auth.services;

import org.springframework.stereotype.Service;
import com.example.auth.config.AuthClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
public class AuthService {

    private final AuthClient authClient;
    private final TokenStorageService tokenStorageService;

    public AuthService(AuthClient authClient, TokenStorageService tokenStorageService) {
        this.authClient = authClient;
        this.tokenStorageService = tokenStorageService;
    }

    public Mono<Map<String, Object>> authenticate(Map<String, String> authRequest) {
        return authClient.authenticate(authRequest)
            .doOnNext(response -> {
                if (response.containsKey("token")) {
                    tokenStorageService.setToken(response.get("token").toString());
                }
            });
    }
}
