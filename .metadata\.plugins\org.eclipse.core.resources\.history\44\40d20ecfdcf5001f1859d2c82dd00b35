package com.example.demo.controller;

import com.example.demo.dto.AuthRequest;
import com.example.demo.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api") // Préfixe commun
public class AuthController {

    private final AuthService authService;

    @Autowired
    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    // 🔹 POST pour envoyer les identifiants dans le body
    @PostMapping("/authenticate")
    public Mono<String> authenticatePost(@RequestBody AuthRequest authRequest) {
        return authService.authenticate(authRequest.getAgency(), authRequest.getUser(), authRequest.getPassword());
    }

    // 🔹 GET pour envoyer les identifiants dans l'URL
    @GetMapping("/authenticate")
    public Mono<String> authenticateGet(@RequestParam String agency, 
                                        @RequestParam String user, 
                                        @RequestParam String password) {
        return authService.authenticate(agency, user, password);
    }
}
