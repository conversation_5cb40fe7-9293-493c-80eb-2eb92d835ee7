<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<section name="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart">
		<item key="group_libraries" value="true"/>
		<item key="layout" value="2"/>
		<item key="rootMode" value="1"/>
		<item key="linkWithEditor" value="false"/>
		<item key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#x0D;&#x0A;&lt;packageExplorer group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;&gt;&#x0D;&#x0A;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;&gt;&#x0D;&#x0A;&lt;xmlDefinedFilters&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;/xmlDefinedFilters&gt;&#x0D;&#x0A;&lt;/customFilters&gt;&#x0D;&#x0A;&lt;/packageExplorer&gt;"/>
	</section>
	<section name="completion_proposal_size">
	</section>
	<section name="quick_assist_proposal_size">
	</section>
	<section name="JavaElementSearchActions">
	</section>
	<section name="NewClassCreationWizard.dialogBounds">
		<item key="DIALOG_X_ORIGIN" value="619"/>
		<item key="DIALOG_Y_ORIGIN" value="9"/>
		<item key="DIALOG_WIDTH" value="700"/>
		<item key="DIALOG_HEIGHT" value="783"/>
		<item key="DIALOG_FONT_NAME" value="1|Segoe UI|9.0|0|WINDOWS|1|-15|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI"/>
	</section>
	<section name="OptionalMessageDialog.hide.">
		<item key="org.eclipse.jdt.ui.typecomment.deprecated" value="true"/>
	</section>
	<section name="NewClassWizardPage">
		<item key="create_constructor" value="false"/>
		<item key="create_unimplemented" value="true"/>
	</section>
	<section name="NewPackageCreationWizard.dialogBounds">
		<item key="DIALOG_X_ORIGIN" value="664"/>
		<item key="DIALOG_Y_ORIGIN" value="374"/>
		<item key="DIALOG_WIDTH" value="700"/>
		<item key="DIALOG_HEIGHT" value="500"/>
		<item key="DIALOG_FONT_NAME" value="1|Segoe UI|9.0|0|WINDOWS|1|-15|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI"/>
	</section>
	<section name="NewPackageWizardPage">
		<item key="create_package_info_java" value="false"/>
	</section>
	<section name="SourceActionDialog.methods">
		<item key="VisibilityModifier" value="1"/>
		<item key="FinalModifier" value="false"/>
		<item key="SynchronizedModifier" value="false"/>
		<item key="Comments" value="false"/>
	</section>
	<section name="AddGetterSetterDialog">
		<item key="SortOrdering" value="false"/>
		<item key="RemoveFinal" value="false"/>
	</section>
	<section name="DialogBounds_GetterSetterTreeSelectionDialog">
		<item key="DIALOG_X_ORIGIN" value="630"/>
		<item key="DIALOG_Y_ORIGIN" value="9"/>
		<item key="DIALOG_WIDTH" value="678"/>
		<item key="DIALOG_HEIGHT" value="889"/>
		<item key="DIALOG_FONT_NAME" value="1|Segoe UI|9.0|0|WINDOWS|1|-15|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI"/>
	</section>
	<section name="RefactoringWizard.preview">
		<item key="width" value="630"/>
		<item key="height" value="400"/>
	</section>
	<section name="org.eclipse.ltk.ui.refactoring.settings">
		<item key="renameSubpackages" value="false"/>
		<item key="updateTextualMatches" value="false"/>
		<item key="updateQualifiedNames" value="false"/>
		<item key="patterns" value="*"/>
		<item key="updateSimilarElements" value="false"/>
		<item key="updateSimilarElementsMatchStrategy" value="1"/>
	</section>
	<section name="NewInterfaceCreationWizard.dialogBounds">
		<item key="DIALOG_X_ORIGIN" value="619"/>
		<item key="DIALOG_Y_ORIGIN" value="95"/>
		<item key="DIALOG_WIDTH" value="700"/>
		<item key="DIALOG_HEIGHT" value="636"/>
		<item key="DIALOG_FONT_NAME" value="1|Segoe UI|9.0|0|WINDOWS|1|-15|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI"/>
	</section>
	<section name="BuildPathsPropertyPage">
		<item key="pageIndex" value="3"/>
	</section>
</section>
