package com.paximum.demo.models;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionResponse {
	class BeginTransactionResponse {
	    private String transactionId;
	    private String expiresOn;

	    public String getTransactionId() { return transactionId; }
	    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
	    public String getExpiresOn() { return expiresOn; }
	    public void setExpiresOn(String expiresOn) { this.expiresOn = expiresOn; }
	}

	class SetReservationInfoResponse {
	    private String transactionId;
	    private boolean isSuccess;

	    public String getTransactionId() { return transactionId; }
	    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
	    public boolean isSuccess() { return isSuccess; }
	    public void setSuccess(boolean success) { isSuccess = success; }
	}

	class CommitTransactionResponse {
	    private String reservationNumber;
	    private String encryptedReservationNumber;

	    public String getReservationNumber() { return reservationNumber; }
	    public void setReservationNumber(String reservationNumber) { this.reservationNumber = reservationNumber; }
	    public String getEncryptedReservationNumber() { return encryptedReservationNumber; }
	    public void setEncryptedReservationNumber(String encryptedReservationNumber) { this.encryptedReservationNumber = encryptedReservationNumber; }
	}

	class SetReservationInfoRequest {
	    public static class Traveller {
	        private String firstName;
	        private String lastName;
	        private String birthDate;
	        private String nationality;

	        public String getFirstName() { return firstName; }
	        public void setFirstName(String firstName) { this.firstName = firstName; }
	        public String getLastName() { return lastName; }
	        public void setLastName(String lastName) { this.lastName = lastName; }
	        public String getBirthDate() { return birthDate; }
	        public void setBirthDate(String birthDate) { this.birthDate = birthDate; }
	        public String getNationality() { return nationality; }
	        public void setNationality(String nationality) { this.nationality = nationality; }
	    }

	    public static class CustomerInfo {
	        private String firstName;
	        private String lastName;
	        private String email;
	        private String phone;

	        public String getFirstName() { return firstName; }
	        public void setFirstName(String firstName) { this.firstName = firstName; }
	        public String getLastName() { return lastName; }
	        public void setLastName(String lastName) { this.lastName = lastName; }
	        public String getEmail() { return email; }
	        public void setEmail(String email) { this.email = email; }
	        public String getPhone() { return phone; }
	        public void setPhone(String phone) { this.phone = phone; }
	    }
	}

	class CommitTransactionRequest {
	    public static class PaymentInformation {
	        private String cardNumber;
	        private String expiryDate;
	        private String cvv;

	        public String getCardNumber() { return cardNumber; }
	        public void setCardNumber(String cardNumber) { this.cardNumber = cardNumber; }
	        public String getExpiryDate() { return expiryDate; }
	        public void setExpiryDate(String expiryDate) { this.expiryDate = expiryDate; }
	        public String getCvv() { return cvv; }
	        public void setCvv(String cvv) { this.cvv = cvv; }
	    }

	    public static class VCCInformation {
	        private String virtualCardNumber;
	        private String amount;

	        public String getVirtualCardNumber() { return virtualCardNumber; }
	        public void setVirtualCardNumber(String virtualCardNumber) { this.virtualCardNumber = virtualCardNumber; }
	        public String getAmount() { return amount; }
	        public void setAmount(String amount) { this.amount = amount; }
	    }
	}
   

}