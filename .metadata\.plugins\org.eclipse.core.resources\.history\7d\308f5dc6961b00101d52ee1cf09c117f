package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BookingTransactionRequest {
    private String action;
    private BeginTransactionRequest beginRequest;
    private SetReservationInfoRequest infoRequest;
    private CommitTransactionRequest commitRequest;

    // Constructor for beginning a transaction
    public BookingTransactionRequest(BeginTransactionRequest beginRequest) {
        this.action = "begin";
        this.beginRequest = beginRequest;
    }

    // Constructor for setting reservation info
    public BookingTransactionRequest(SetReservationInfoRequest infoRequest) {
        this.action = "setInfo";
        this.infoRequest = infoRequest;
    }

    // Constructor for committing a transaction
    public BookingTransactionRequest(CommitTransactionRequest commitRequest) {
        this.action = "commit";
        this.commitRequest = commitRequest;
    }

    // Default constructor for Jackson
    public BookingTransactionRequest() {
    }

    // Getters and Setters
    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public BeginTransactionRequest getBeginRequest() {
        return beginRequest;
    }

    public void setBeginRequest(BeginTransactionRequest beginRequest) {
        this.beginRequest = beginRequest;
    }

    public SetReservationInfoRequest getInfoRequest() {
        return infoRequest;
    }

    public void setInfoRequest(SetReservationInfoRequest infoRequest) {
        this.infoRequest = infoRequest;
    }

    public CommitTransactionRequest getCommitRequest() {
        return commitRequest;
    }

    public void setCommitRequest(CommitTransactionRequest commitRequest) {
        this.commitRequest = commitRequest;
    }
}