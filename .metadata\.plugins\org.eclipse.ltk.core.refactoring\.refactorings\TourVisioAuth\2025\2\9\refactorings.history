<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;TourVisioAuth&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuth&apos;&#x0D;&#x0A;- Original element: &apos;TourVisioAuth/src/main/java/com.example.config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.config" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740749455277" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;TourVisioAuth&apos;&#x0D;&#x0A;- Original project: &apos;TourVisioAuth&apos;&#x0D;&#x0A;- Original element: &apos;TourVisioAuth/src/main/java/com.example.demo.config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.example.demo.config" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1740749479174" subPackages="false" version="1.0"/>
</session>