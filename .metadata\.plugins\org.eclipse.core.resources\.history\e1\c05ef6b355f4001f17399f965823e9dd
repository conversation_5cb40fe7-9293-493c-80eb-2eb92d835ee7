package com.e_tourism.auth.service;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class AuthService {
    
    private static final String LOGIN_URL = "https://api.tourvisio.com/api/authenticationservice/login";
    
    private final RestTemplate restTemplate;

    public AuthService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public String authenticate() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        
        String requestBody = "{\"Agency\":\"B2B\", \"User\":\"B2B\", \"Password\":\"123456\"}";
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<String> response = restTemplate.exchange(LOGIN_URL, HttpMethod.POST, entity, String.class);
        return response.getBody();
    }
}