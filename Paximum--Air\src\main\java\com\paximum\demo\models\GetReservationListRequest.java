package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetReservationListRequest {
    private String culture;
    private List<DateCriteria> dateCriterias;
    private Integer reservationStatus;
    private Integer limit;
    private Integer pageRowCount;
    private Integer maxIndexNumber;
    private Integer minIndexNumber;

    // Constructors
    public GetReservationListRequest() {}

    // Getters and Setters
    public String getCulture() { return culture; }
    public void setCulture(String culture) { this.culture = culture; }
    public List<DateCriteria> getDateCriterias() { return dateCriterias; }
    public void setDateCriterias(List<DateCriteria> dateCriterias) { this.dateCriterias = dateCriterias; }
    public Integer getReservationStatus() { return reservationStatus; }
    public void setReservationStatus(Integer reservationStatus) { this.reservationStatus = reservationStatus; }
    public Integer getLimit() { return limit; }
    public void setLimit(Integer limit) { this.limit = limit; }
    public Integer getPageRowCount() { return pageRowCount; }
    public void setPageRowCount(Integer pageRowCount) { this.pageRowCount = pageRowCount; }
    public Integer getMaxIndexNumber() { return maxIndexNumber; }
    public void setMaxIndexNumber(Integer maxIndexNumber) { this.maxIndexNumber = maxIndexNumber; }
    public Integer getMinIndexNumber() { return minIndexNumber; }
    public void setMinIndexNumber(Integer minIndexNumber) { this.minIndexNumber = minIndexNumber; }

    // Nested static class for DateCriteria
    public static class DateCriteria {
        private Integer type;
        private String from;
        private String to;

        public DateCriteria() {}

        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public String getFrom() { return from; }
        public void setFrom(String from) { this.from = from; }
        public String getTo() { return to; }
        public void setTo(String to) { this.to = to; }
    }
}