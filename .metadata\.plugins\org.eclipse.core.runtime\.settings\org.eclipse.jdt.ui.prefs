content_assist_favorite_static_members=org.assertj.core.api.Assertions.*;org.mockito.Matchers.*;org.mockito.Mockito.*;org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestBuilders.*;org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.*;org.springframework.security.test.web.servlet.response.SecurityMockMvcResultMatchers.*;org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.*;org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.*;org.springframework.test.web.client.match.MockRestRequestMatchers.*;org.springframework.test.web.client.response.MockRestResponseCreators.*;org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;org.hamcrest.CoreMatchers.*;org.junit.Assert.*
content_assist_lru_history=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?><history maxLHS\="100" maxRHS\="10"><lhs name\="com.paximum.demo.models.BeginTransactionResponse$Header"><rhs name\="com.paximum.demo.models.BeginTransactionResponse$Header"/></lhs><lhs name\="com.paximum.demo.models.SetReservationInfoResponse$Header"><rhs name\="com.paximum.demo.models.SetReservationInfoResponse$Header"/></lhs></history>
content_assist_number_of_computers=14
content_assist_proposals_background=255,255,255
content_assist_proposals_foreground=0,0,0
eclipse.preferences.version=1
org.eclipse.jdt.ui.formatterprofiles.version=23
spelling_locale_initialized=true
typefilter_migrated_2=true
useAnnotationsPrefPage=true
useQuickDiffPrefPage=true
