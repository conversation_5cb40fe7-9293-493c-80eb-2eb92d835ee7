    590 0org.springframework.ide.eclipse.boot.dash.docker 590 0org.springframework.ide.eclipse.boot.dash.docker 470 org.eclipse.ui.editors 470 org.eclipse.ui.editors 591 +org.springframework.ide.eclipse.boot.launch 591 +org.springframework.ide.eclipse.boot.launch 471 org.eclipse.ui.externaltools 471 org.eclipse.ui.externaltools 592 0org.springframework.ide.eclipse.boot.refactoring 592 0org.springframework.ide.eclipse.boot.refactoring 472 org.eclipse.ui.forms 472 org.eclipse.ui.forms 593 ,org.springframework.ide.eclipse.boot.restart 593 ,org.springframework.ide.eclipse.boot.restart 231 (org.eclipse.equinox.p2.publisher.eclipse 231 (org.eclipse.equinox.p2.publisher.eclipse 352 org.eclipse.m2e.binaryproject 352 org.eclipse.m2e.binaryproject 473 org.eclipse.ui.genericeditor 473 org.eclipse.ui.genericeditor 594 /org.springframework.ide.eclipse.boot.validation 594 /org.springframework.ide.eclipse.boot.validation 232 )org.eclipse.equinox.p2.reconciler.dropins 232 )org.eclipse.equinox.p2.reconciler.dropins 353  org.eclipse.m2e.binaryproject.ui 353  org.eclipse.m2e.binaryproject.ui 474 org.eclipse.ui.ide 474 org.eclipse.ui.ide 595 +org.springframework.ide.eclipse.boot.wizard 595 +org.springframework.ide.eclipse.boot.wizard 354 org.eclipse.m2e.core 354 org.eclipse.m2e.core 475 org.eclipse.ui.ide.application 475 org.eclipse.ui.ide.application 596 +org.springframework.ide.eclipse.buildship30 596 +org.springframework.ide.eclipse.buildship30 234 'org.eclipse.equinox.p2.repository.tools 234 'org.eclipse.equinox.p2.repository.tools 355 org.eclipse.m2e.core.ui 355 org.eclipse.m2e.core.ui 476 org.eclipse.ui.intro 476 org.eclipse.ui.intro 235 )org.eclipse.equinox.p2.touchpoint.eclipse 235 )org.eclipse.equinox.p2.touchpoint.eclipse 356 org.eclipse.m2e.discovery 356 org.eclipse.m2e.discovery 477 org.eclipse.ui.intro.quicklinks 477 org.eclipse.ui.intro.quicklinks 236 )org.eclipse.equinox.p2.touchpoint.natives 236 )org.eclipse.equinox.p2.touchpoint.natives 357 org.eclipse.m2e.editor 357 org.eclipse.m2e.editor 478 org.eclipse.ui.intro.universal 478 org.eclipse.ui.intro.universal 599 'org.springframework.ide.eclipse.imports 599 'org.springframework.ide.eclipse.imports 116 org.eclipse.ant.core 116 org.eclipse.ant.core 358 org.eclipse.m2e.editor.lemminx 358 org.eclipse.m2e.editor.lemminx 479 org.eclipse.ui.monitoring 479 org.eclipse.ui.monitoring 117 org.eclipse.ant.launching 117 org.eclipse.ant.launching 238 org.eclipse.equinox.p2.ui 238 org.eclipse.equinox.p2.ui 359 org.eclipse.m2e.jdt 359 org.eclipse.m2e.jdt 118 org.eclipse.ant.ui 118 org.eclipse.ant.ui 239 #org.eclipse.equinox.p2.ui.discovery 239 #org.eclipse.equinox.p2.ui.discovery 119 org.eclipse.buildship.branding 119 org.eclipse.buildship.branding 10 bndtools.jareditor 10 bndtools.jareditor 480 org.eclipse.ui.navigator 480 org.eclipse.ui.navigator 360 org.eclipse.m2e.jdt.ui 360 org.eclipse.m2e.jdt.ui 481 "org.eclipse.ui.navigator.resources 481 "org.eclipse.ui.navigator.resources 240 &org.eclipse.equinox.p2.ui.importexport 240 &org.eclipse.equinox.p2.ui.importexport 361 org.eclipse.m2e.launching 361 org.eclipse.m2e.launching 482 org.eclipse.ui.net 482 org.eclipse.ui.net 241 org.eclipse.equinox.p2.ui.sdk 241 org.eclipse.equinox.p2.ui.sdk 483 org.eclipse.ui.themes 483 org.eclipse.ui.themes 121 org.eclipse.buildship.core 121 org.eclipse.buildship.core 242 'org.eclipse.equinox.p2.ui.sdk.scheduler 242 'org.eclipse.equinox.p2.ui.sdk.scheduler 484 org.eclipse.ui.trace 484 org.eclipse.ui.trace 122 org.eclipse.buildship.ui 122 org.eclipse.buildship.ui 364 org.eclipse.m2e.mavenarchiver 364 org.eclipse.m2e.mavenarchiver 485 org.eclipse.ui.views 485 org.eclipse.ui.views 123 org.eclipse.cdt.core.native 123 org.eclipse.cdt.core.native 244 !org.eclipse.equinox.p2.updatesite 244 !org.eclipse.equinox.p2.updatesite 365 org.eclipse.m2e.model.edit 365 org.eclipse.m2e.model.edit 486 org.eclipse.ui.views.log 486 org.eclipse.ui.views.log 124 org.eclipse.cdt.core.win32 123 org.eclipse.cdt.core.native 245 org.eclipse.equinox.preferences 245 org.eclipse.equinox.preferences 487 &org.eclipse.ui.views.properties.tabbed 487 &org.eclipse.ui.views.properties.tabbed 246 org.eclipse.equinox.registry 246 org.eclipse.equinox.registry 367 org.eclipse.m2e.profiles.ui 367 org.eclipse.m2e.profiles.ui 126 org.eclipse.compare 126 org.eclipse.compare 247 org.eclipse.equinox.security 247 org.eclipse.equinox.security 368 org.eclipse.m2e.refactoring 368 org.eclipse.m2e.refactoring 489 org.eclipse.ui.workbench 489 org.eclipse.ui.workbench 248 org.eclipse.equinox.security.ui 248 org.eclipse.equinox.security.ui 369 org.eclipse.m2e.scm 369 org.eclipse.m2e.scm 128 org.eclipse.compare.win32 128 org.eclipse.compare.win32 249 "org.eclipse.equinox.security.win32 247 org.eclipse.equinox.security 490 #org.eclipse.ui.workbench.texteditor 490 #org.eclipse.ui.workbench.texteditor 370 org.eclipse.m2e.sourcelookup 370 org.eclipse.m2e.sourcelookup 371 org.eclipse.m2e.sourcelookup.ui 371 org.eclipse.m2e.sourcelookup.ui 492 org.eclipse.urischeme 492 org.eclipse.urischeme 130 org.eclipse.core.contenttype 130 org.eclipse.core.contenttype 493 org.eclipse.userstorage 493 org.eclipse.userstorage 252 org.eclipse.help 252 org.eclipse.help 373 org.eclipse.m2e.wtp 373 org.eclipse.m2e.wtp 494 org.eclipse.userstorage.oauth 494 org.eclipse.userstorage.oauth 253 org.eclipse.help.base 253 org.eclipse.help.base 374 org.eclipse.m2e.wtp.overlay 374 org.eclipse.m2e.wtp.overlay 495 org.eclipse.userstorage.ui 495 org.eclipse.userstorage.ui 254 org.eclipse.help.ui 254 org.eclipse.help.ui 375 org.eclipse.m2e.wtp.overlay.ui 375 org.eclipse.m2e.wtp.overlay.ui 496 org.eclipse.wildwebdeveloper 496 org.eclipse.wildwebdeveloper 255 org.eclipse.help.webapp 255 org.eclipse.help.webapp 376 org.eclipse.mylyn.commons.core 376 org.eclipse.mylyn.commons.core 135 org.eclipse.core.expressions 135 org.eclipse.core.expressions 256 org.eclipse.jdt 256 org.eclipse.jdt 136 org.eclipse.core.externaltools 136 org.eclipse.core.externaltools 499  org.eclipse.wildwebdeveloper.xml 499  org.eclipse.wildwebdeveloper.xml 137 org.eclipse.core.filebuffers 137 org.eclipse.core.filebuffers 258 org.eclipse.jdt.apt.core 258 org.eclipse.jdt.apt.core 138 org.eclipse.core.filesystem 138 org.eclipse.core.filesystem 259 "org.eclipse.jdt.apt.pluggable.core 259 "org.eclipse.jdt.apt.pluggable.core 139 org.eclipse.core.jobs 139 org.eclipse.core.jobs 260 org.eclipse.jdt.apt.ui 260 org.eclipse.jdt.apt.ui 381 *org.eclipse.mylyn.commons.notifications.ui 381 *org.eclipse.mylyn.commons.notifications.ui 140 org.eclipse.core.net 140 org.eclipse.core.net 261 org.eclipse.jdt.bcoview 261 org.eclipse.jdt.bcoview 382 +org.eclipse.mylyn.commons.repositories.core 382 +org.eclipse.mylyn.commons.repositories.core 141 org.eclipse.core.resources 141 org.eclipse.core.resources 262 org.eclipse.jdt.core 262 org.eclipse.jdt.core 383 )org.eclipse.mylyn.commons.repositories.ui 383 )org.eclipse.mylyn.commons.repositories.ui 142 org.eclipse.core.runtime 142 org.eclipse.core.runtime 143 org.eclipse.core.variables 143 org.eclipse.core.variables 264 !org.eclipse.jdt.core.formatterapp 264 !org.eclipse.jdt.core.formatterapp 144 org.eclipse.debug.core 144 org.eclipse.debug.core 265 !org.eclipse.jdt.core.manipulation 265 !org.eclipse.jdt.core.manipulation 386 #org.eclipse.mylyn.commons.workbench 386 #org.eclipse.mylyn.commons.workbench 145 org.eclipse.debug.ui 145 org.eclipse.debug.ui 266 org.eclipse.jdt.debug 266 org.eclipse.jdt.debug 387 org.eclipse.mylyn.context.core 387 org.eclipse.mylyn.context.core 146 org.eclipse.debug.ui.launchview 146 org.eclipse.debug.ui.launchview 267 org.eclipse.jdt.debug.ui 267 org.eclipse.jdt.debug.ui 388 org.eclipse.mylyn.context.ui 388 org.eclipse.mylyn.context.ui 268 org.eclipse.jdt.doc.user 268 org.eclipse.jdt.doc.user 269 org.eclipse.jdt.junit 269 org.eclipse.jdt.junit 390 org.eclipse.mylyn.monitor.ui 390 org.eclipse.mylyn.monitor.ui 270 org.eclipse.jdt.junit.core 270 org.eclipse.jdt.junit.core 391 org.eclipse.mylyn.tasks.core 391 org.eclipse.mylyn.tasks.core 392 org.eclipse.mylyn.tasks.ui 392 org.eclipse.mylyn.tasks.ui 274 org.eclipse.jdt.launching 274 org.eclipse.jdt.launching 275 org.eclipse.jdt.ui 275 org.eclipse.jdt.ui 396 &org.eclipse.mylyn.wikitext.asciidoc.ui 396 &org.eclipse.mylyn.wikitext.asciidoc.ui 276 org.eclipse.jem 276 org.eclipse.jem 156 org.eclipse.e4.ui.css.core 156 org.eclipse.e4.ui.css.core 277 org.eclipse.jem.util 277 org.eclipse.jem.util 398 (org.eclipse.mylyn.wikitext.confluence.ui 398 (org.eclipse.mylyn.wikitext.confluence.ui 157 org.eclipse.e4.ui.css.swt 157 org.eclipse.e4.ui.css.swt 278 org.eclipse.jem.workbench 278 org.eclipse.jem.workbench 399 %org.eclipse.mylyn.wikitext.context.ui 399 %org.eclipse.mylyn.wikitext.context.ui 158 org.eclipse.e4.ui.css.swt.theme 158 org.eclipse.e4.ui.css.swt.theme 162 !org.eclipse.e4.ui.model.workbench 162 !org.eclipse.e4.ui.model.workbench 165 org.eclipse.e4.ui.swt.win32 158 org.eclipse.e4.ui.css.swt.theme 167 org.eclipse.e4.ui.workbench 167 org.eclipse.e4.ui.workbench 600 )org.springframework.ide.eclipse.maven.pom 600 )org.springframework.ide.eclipse.maven.pom 601 .org.springframework.ide.eclipse.xml.namespaces 601 .org.springframework.ide.eclipse.xml.namespaces 602 #org.springframework.tooling.boot.ls 602 #org.springframework.tooling.boot.ls 605 .org.springframework.tooling.ls.eclipse.commons 605 .org.springframework.tooling.ls.eclipse.commons 606 1org.springframework.tooling.ls.eclipse.gotosymbol 606 1org.springframework.tooling.ls.eclipse.gotosymbol 607 ,org.springsource.ide.eclipse.commons.boot.ls 607 ,org.springsource.ide.eclipse.commons.boot.ls 608 )org.springsource.ide.eclipse.commons.core 608 )org.springsource.ide.eclipse.commons.core 170 org.eclipse.e4.ui.workbench.swt 170 org.eclipse.e4.ui.workbench.swt 172 org.eclipse.ecf 172 org.eclipse.ecf 173 org.eclipse.ecf.filetransfer 173 org.eclipse.ecf.filetransfer 174 org.eclipse.ecf.identity 174 org.eclipse.ecf.identity 175 %org.eclipse.ecf.provider.filetransfer 175 %org.eclipse.ecf.provider.filetransfer 176 1org.eclipse.ecf.provider.filetransfer.httpclient5 176 1org.eclipse.ecf.provider.filetransfer.httpclient5 178 4org.eclipse.ecf.provider.filetransfer.httpclientjava 178 4org.eclipse.ecf.provider.filetransfer.httpclientjava 613 'org.springsource.ide.eclipse.commons.ui 613 'org.springsource.ide.eclipse.commons.ui 182 org.eclipse.egit.core 182 org.eclipse.egit.core 183 org.eclipse.egit.doc 183 org.eclipse.egit.doc 185 org.eclipse.egit.gitflow.ui 185 org.eclipse.egit.gitflow.ui 186 org.eclipse.egit.ui 186 org.eclipse.egit.ui 187 org.eclipse.emf.codegen 187 org.eclipse.emf.codegen 189 org.eclipse.emf.ecore 189 org.eclipse.emf.ecore 500 org.eclipse.wst.common.core 500 org.eclipse.wst.common.core 501 org.eclipse.wst.common.emf 501 org.eclipse.wst.common.emf 502 /org.eclipse.wst.common.emfworkbench.integration 502 /org.eclipse.wst.common.emfworkbench.integration 504 !org.eclipse.wst.common.frameworks 504 !org.eclipse.wst.common.frameworks 505 !org.eclipse.wst.common.modulecore 505 !org.eclipse.wst.common.modulecore 506 $org.eclipse.wst.common.modulecore.ui 506 $org.eclipse.wst.common.modulecore.ui 507 )org.eclipse.wst.common.project.facet.core 507 )org.eclipse.wst.common.project.facet.core 508 'org.eclipse.wst.common.project.facet.ui 508 'org.eclipse.wst.common.project.facet.ui 509 "org.eclipse.wst.common.uriresolver 509 "org.eclipse.wst.common.uriresolver 190 org.eclipse.emf.ecore.change 190 org.eclipse.emf.ecore.change 191 org.eclipse.emf.ecore.edit 191 org.eclipse.emf.ecore.edit 192 org.eclipse.emf.ecore.xmi 192 org.eclipse.emf.ecore.xmi 193 org.eclipse.emf.edit 193 org.eclipse.emf.edit 196 org.eclipse.epp.mpc.help.ui 196 org.eclipse.epp.mpc.help.ui 197 org.eclipse.epp.mpc.ui 197 org.eclipse.epp.mpc.ui 198 org.eclipse.epp.mpc.ui.css 198 org.eclipse.epp.mpc.ui.css 199 org.eclipse.equinox.app 199 org.eclipse.equinox.app 510 %org.eclipse.wst.internet.monitor.core 510 %org.eclipse.wst.internet.monitor.core 511 #org.eclipse.wst.internet.monitor.ui 511 #org.eclipse.wst.internet.monitor.ui 512 org.eclipse.wst.server.core 512 org.eclipse.wst.server.core 514  org.eclipse.wst.server.http.core 514  org.eclipse.wst.server.http.core 515 org.eclipse.wst.server.http.ui 515 org.eclipse.wst.server.http.ui 516 org.eclipse.wst.server.preview 516 org.eclipse.wst.server.preview 517 &org.eclipse.wst.server.preview.adapter 517 &org.eclipse.wst.server.preview.adapter 518 org.eclipse.wst.server.ui 518 org.eclipse.wst.server.ui 519 "org.eclipse.wst.server.ui.doc.user 519 "org.eclipse.wst.server.ui.doc.user 520 !org.eclipse.wst.server.ui.infopop 520 !org.eclipse.wst.server.ui.infopop 400 "org.eclipse.mylyn.wikitext.help.ui 400 "org.eclipse.mylyn.wikitext.help.ui 521 org.eclipse.wst.sse.core 521 org.eclipse.wst.sse.core 522 org.eclipse.wst.sse.ui 522 org.eclipse.wst.sse.ui 523 org.eclipse.wst.validation 523 org.eclipse.wst.validation 403 &org.eclipse.mylyn.wikitext.markdown.ui 403 &org.eclipse.mylyn.wikitext.markdown.ui 524 org.eclipse.wst.web 524 org.eclipse.wst.web 525 org.eclipse.wst.xml.core 525 org.eclipse.wst.xml.core 405 'org.eclipse.mylyn.wikitext.mediawiki.ui 405 'org.eclipse.mylyn.wikitext.mediawiki.ui 407 #org.eclipse.mylyn.wikitext.tasks.ui 407 #org.eclipse.mylyn.wikitext.tasks.ui 409 %org.eclipse.mylyn.wikitext.textile.ui 409 %org.eclipse.mylyn.wikitext.textile.ui 411 &org.eclipse.mylyn.wikitext.tracwiki.ui 411 &org.eclipse.mylyn.wikitext.tracwiki.ui 413 #org.eclipse.mylyn.wikitext.twiki.ui 413 #org.eclipse.mylyn.wikitext.twiki.ui 414 org.eclipse.mylyn.wikitext.ui 414 org.eclipse.mylyn.wikitext.ui 416 org.eclipse.osgi 416 org.eclipse.osgi 539 org.jboss.tools.m2e.wro4j.core 539 org.jboss.tools.m2e.wro4j.core 419 org.eclipse.pde 419 org.eclipse.pde 540 org.jboss.tools.m2e.wro4j.ui 540 org.jboss.tools.m2e.wro4j.ui 420 org.eclipse.pde.api.tools 420 org.eclipse.pde.api.tools 422 org.eclipse.pde.api.tools.ui 422 org.eclipse.pde.api.tools.ui 423 org.eclipse.pde.bnd.ui 423 org.eclipse.pde.bnd.ui 424 org.eclipse.pde.build 424 org.eclipse.pde.build 425 org.eclipse.pde.core 425 org.eclipse.pde.core 426 org.eclipse.pde.doc.user 426 org.eclipse.pde.doc.user 427 org.eclipse.pde.ds.annotations 427 org.eclipse.pde.ds.annotations 428 org.eclipse.pde.ds.core 428 org.eclipse.pde.ds.core 429 org.eclipse.pde.ds.ui 429 org.eclipse.pde.ds.ui 430 'org.eclipse.pde.genericeditor.extension 430 'org.eclipse.pde.genericeditor.extension 310 org.eclipse.jsch.core 310 org.eclipse.jsch.core 431 org.eclipse.pde.junit.runtime 431 org.eclipse.pde.junit.runtime 311 org.eclipse.jsch.ui 311 org.eclipse.jsch.ui 432 org.eclipse.pde.launching 432 org.eclipse.pde.launching 312 -org.eclipse.jst.common.annotations.controller 312 -org.eclipse.jst.common.annotations.controller 433 org.eclipse.pde.runtime 433 org.eclipse.pde.runtime 313 'org.eclipse.jst.common.annotations.core 313 'org.eclipse.jst.common.annotations.core 434 org.eclipse.pde.ua.core 434 org.eclipse.pde.ua.core 314 !org.eclipse.jst.common.frameworks 314 !org.eclipse.jst.common.frameworks 435 org.eclipse.pde.ua.ui 435 org.eclipse.pde.ua.ui 315 )org.eclipse.jst.common.project.facet.core 315 )org.eclipse.jst.common.project.facet.core 436 org.eclipse.pde.ui 436 org.eclipse.pde.ui 316 'org.eclipse.jst.common.project.facet.ui 316 'org.eclipse.jst.common.project.facet.ui 437 org.eclipse.pde.ui.templates 437 org.eclipse.pde.ui.templates 317 org.eclipse.jst.j2ee 317 org.eclipse.jst.j2ee 438 org.eclipse.platform 438 org.eclipse.platform 318 org.eclipse.jst.j2ee.core 318 org.eclipse.jst.j2ee.core 439 org.eclipse.platform.doc.user 439 org.eclipse.platform.doc.user 319 org.eclipse.jst.j2ee.ejb 319 org.eclipse.jst.j2ee.ejb 320 org.eclipse.jst.j2ee.jca 320 org.eclipse.jst.j2ee.jca 441 org.eclipse.search 441 org.eclipse.search 200 org.eclipse.equinox.bidi 200 org.eclipse.equinox.bidi 321 org.eclipse.jst.j2ee.web 321 org.eclipse.jst.j2ee.web 442 org.eclipse.search.core 442 org.eclipse.search.core 322 org.eclipse.jst.jee 322 org.eclipse.jst.jee 323 org.eclipse.jst.jee.ejb 323 org.eclipse.jst.jee.ejb 324 org.eclipse.jst.jee.web 324 org.eclipse.jst.jee.web 445 org.eclipse.team.core 445 org.eclipse.team.core 325 org.eclipse.jst.server.core 325 org.eclipse.jst.server.core 446 -org.eclipse.team.genericeditor.diff.extension 446 -org.eclipse.team.genericeditor.diff.extension 326 &org.eclipse.jst.server.preview.adapter 326 &org.eclipse.jst.server.preview.adapter 447 org.eclipse.team.ui 447 org.eclipse.team.ui 327 "org.eclipse.jst.server.tomcat.core 327 "org.eclipse.jst.server.tomcat.core 328  org.eclipse.jst.server.tomcat.ui 328  org.eclipse.jst.server.tomcat.ui 449 org.eclipse.text.quicksearch 449 org.eclipse.text.quicksearch 208 !org.eclipse.equinox.http.registry 208 !org.eclipse.equinox.http.registry 329 org.eclipse.jst.server.ui 329 org.eclipse.jst.server.ui 450 org.eclipse.tips.core 450 org.eclipse.tips.core 451 org.eclipse.tips.ide 451 org.eclipse.tips.ide 332 "org.eclipse.linuxtools.docker.core 332 "org.eclipse.linuxtools.docker.core 333 "org.eclipse.linuxtools.docker.docs 333 "org.eclipse.linuxtools.docker.docs 454 'org.eclipse.tm.terminal.connector.local 454 'org.eclipse.tm.terminal.connector.local 334 'org.eclipse.linuxtools.docker.editor.ls 334 'org.eclipse.linuxtools.docker.editor.ls 455 )org.eclipse.tm.terminal.connector.process 455 )org.eclipse.tm.terminal.connector.process 335  org.eclipse.linuxtools.docker.ui 335  org.eclipse.linuxtools.docker.ui 456 %org.eclipse.tm.terminal.connector.ssh 456 %org.eclipse.tm.terminal.connector.ssh 215 *org.eclipse.equinox.p2.artifact.repository 215 *org.eclipse.equinox.p2.artifact.repository 336 *org.eclipse.linuxtools.jdt.docker.launcher 336 *org.eclipse.linuxtools.jdt.docker.launcher 457 (org.eclipse.tm.terminal.connector.telnet 457 (org.eclipse.tm.terminal.connector.telnet 337 org.eclipse.lsp4e 337 org.eclipse.lsp4e 458 org.eclipse.tm.terminal.control 458 org.eclipse.tm.terminal.control 338 org.eclipse.lsp4e.debug 338 org.eclipse.lsp4e.debug 459 !org.eclipse.tm.terminal.view.core 459 !org.eclipse.tm.terminal.view.core 339 org.eclipse.lsp4e.jdt 339 org.eclipse.lsp4e.jdt 219 #org.eclipse.equinox.p2.director.app 219 #org.eclipse.equinox.p2.director.app 460 org.eclipse.tm.terminal.view.ui 460 org.eclipse.tm.terminal.view.ui 462 org.eclipse.tm4e.language_pack 462 org.eclipse.tm4e.language_pack 463 &org.eclipse.tm4e.languageconfiguration 463 &org.eclipse.tm4e.languageconfiguration 222 .org.eclipse.equinox.p2.discovery.compatibility 222 .org.eclipse.equinox.p2.discovery.compatibility 464 org.eclipse.tm4e.registry 464 org.eclipse.tm4e.registry 585 org.sonatype.m2e.egit 585 org.sonatype.m2e.egit 223 org.eclipse.equinox.p2.engine 223 org.eclipse.equinox.p2.engine 344  org.eclipse.ltk.core.refactoring 344  org.eclipse.ltk.core.refactoring 465 org.eclipse.tm4e.ui 465 org.eclipse.tm4e.ui 586 %org.springframework.boot.ide.branding 586 %org.springframework.boot.ide.branding 224 (org.eclipse.equinox.p2.extensionlocation 224 (org.eclipse.equinox.p2.extensionlocation 345 org.eclipse.ltk.ui.refactoring 345 org.eclipse.ltk.ui.refactoring 466 org.eclipse.ui 466 org.eclipse.ui 225 'org.eclipse.equinox.p2.garbagecollector 225 'org.eclipse.equinox.p2.garbagecollector 346 org.eclipse.m2e.apt.core 346 org.eclipse.m2e.apt.core 467 org.eclipse.ui.browser 467 org.eclipse.ui.browser 588 $org.springframework.ide.eclipse.boot 588 $org.springframework.ide.eclipse.boot 226 #org.eclipse.equinox.p2.jarprocessor 226 #org.eclipse.equinox.p2.jarprocessor 347 org.eclipse.m2e.apt.ui 347 org.eclipse.m2e.apt.ui 468 org.eclipse.ui.cheatsheets 468 org.eclipse.ui.cheatsheets 589 )org.springframework.ide.eclipse.boot.dash 589 )org.springframework.ide.eclipse.boot.dash 469 org.eclipse.ui.console 469 org.eclipse.ui.console 228 *org.eclipse.equinox.p2.metadata.repository 228 *org.eclipse.equinox.p2.metadata.repository