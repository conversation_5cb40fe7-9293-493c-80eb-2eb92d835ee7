<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;config" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741771863056" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/config&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;config" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1741771897282" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.config&apos; to &apos;com.paximum.auth.config&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.config&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.config&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.config" name="com.paximum.auth.config" qualified="false" references="true" stamp="1741772039503" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.controllers&apos; to &apos;com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.controllers&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.controllers&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.controllers" name="com.paximum.auth.controllers" qualified="false" references="true" stamp="1741772057173" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.models&apos; to &apos;com.paximum.auth.models&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.models&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.auth.models&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.models&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.models" name="com.paximum.auth.models" qualified="false" references="true" stamp="1741772072865" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename package &apos;com.paximum.services&apos; to &apos;com.paximum.auth.services&apos;&#x0D;&#x0A;- Original project: &apos;Paximum--Air&apos;&#x0D;&#x0A;- Original element: &apos;Paximum--Air/src/main/java/com.paximum.services&apos;&#x0D;&#x0A;- Renamed element: &apos;Paximum--Air/src/main/java/com.paximum.auth.services&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename package &apos;com.paximum.services&apos;" flags="589830" hierarchical="false" id="org.eclipse.jdt.ui.rename.package" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.services" name="com.paximum.auth.services" qualified="false" references="true" stamp="1741772087708" textual="false" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.config&apos; to &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;Paximum-------------Air&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;Paximum-------------Air/src/main/java/com.paximum.auth.config&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.config" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741772818560" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.models&apos; to &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;Paximum-------------Air&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;Paximum-------------Air/src/main/java/com.paximum.auth.models&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.models" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741772823014" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.controllers&apos; to &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;Paximum-------------Air&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;Paximum-------------Air/src/main/java/com.paximum.auth.controllers&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.controllers" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741772826423" version="1.0"/>&#x0A;<refactoring comment="Move package &apos;com.paximum.auth.services&apos; to &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original project: &apos;Paximum-------------Air&apos;&#x0D;&#x0A;- Destination element: &apos;Paximum--Air/src/main/java&apos;&#x0D;&#x0A;- Original element: &apos;Paximum-------------Air/src/main/java/com.paximum.auth.services&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move package" destination="=Paximum--Air/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.paximum.auth.services" flags="589830" fragments="1" id="org.eclipse.jdt.ui.move" policy="org.eclipse.jdt.ui.movePackages" stamp="1741772828609" version="1.0"/>
</session>