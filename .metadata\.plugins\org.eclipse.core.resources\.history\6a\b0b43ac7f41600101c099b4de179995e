package com.paximum.demo.models;



import java.util.List;


public class BeginTransactionResponse {
    private Header header;
    private Body body;

    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }

    public static class Header {
        private String requestId;
        private boolean success;
        private String responseTime;
        private List<Message> messages;

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(String responseTime) {
            this.responseTime = responseTime;
        }

        public List<Message> getMessages() {
            return messages;
        }

        public void setMessages(List<Message> messages) {
            this.messages = messages;
        }
    }

    public static class Message {
        private int id;
        private String code;
        private int messageType;
        private String message;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getMessageType() {
            return messageType;
        }

        public void setMessageType(int messageType) {
            this.messageType = messageType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    public static class Body {
        private String transactionId;
        private String expiresOn;
        private ReservationData reservationData;
        private int status;
        private int transactionType;

        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public String getExpiresOn() {
            return expiresOn;
        }

        public void setExpiresOn(String expiresOn) {
            this.expiresOn = expiresOn;
        }

        public ReservationData getReservationData() {
            return reservationData;
        }

        public void setReservationData(ReservationData reservationData) {
            this.reservationData = reservationData;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getTransactionType() {
            return transactionType;
        }

        public void setTransactionType(int transactionType) {
            this.transactionType = transactionType;
        }
    }

    public static class ReservationData {
        private List<Traveller> travellers;
        private ReservationInfo reservationInfo;
        private List<Service> services;
        private PaymentDetail paymentDetail;
        private List<Invoice> invoices;

        public List<Traveller> getTravellers() {
            return travellers;
        }

        public void setTravellers(List<Traveller> travellers) {
            this.travellers = travellers;
        }

        public ReservationInfo getReservationInfo() {
            return reservationInfo;
        }

        public void setReservationInfo(ReservationInfo reservationInfo) {
            this.reservationInfo = reservationInfo;
        }

        public List<Service> getServices() {
            return services;
        }

        public void setServices(List<Service> services) {
            this.services = services;
        }

        public PaymentDetail getPaymentDetail() {
            return paymentDetail;
        }

        public void setPaymentDetail(PaymentDetail paymentDetail) {
            this.paymentDetail = paymentDetail;
        }

        public List<Invoice> getInvoices() {
            return invoices;
        }

        public void setInvoices(List<Invoice> invoices) {
            this.invoices = invoices;
        }
    }

    public static class Traveller {
        private String travellerId;
        private int type;
        private int title;
        private List<Title> availableTitles;
        private List<AcademicTitle> availableAcademicTitles;
        private boolean isLeader;
        private String birthDate;
        private Nationality nationality;
        private String identityNumber;
        private PassportInfo passportInfo;
        private Address address;
        private DestinationAddress destinationAddress;
        private List<Service> services;
        private int orderNumber;
        private String birthDateFrom;
        private String birthDateTo;
        private List<String> requiredFields;
        private List<Document> documents;
        private int passengerType;
        private AdditionalFields additionalFields;
        private List<InsertField> insertFields;
        private int status;

        public String getTravellerId() {
            return travellerId;
        }

        public void setTravellerId(String travellerId) {
            this.travellerId = travellerId;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getTitle() {
            return title;
        }

        public void setTitle(int title) {
            this.title = title;
        }

        public List<Title> getAvailableTitles() {
            return availableTitles;
        }

        public void setAvailableTitles(List<Title> availableTitles) {
            this.availableTitles = availableTitles;
        }

        public List<AcademicTitle> getAvailableAcademicTitles() {
            return availableAcademicTitles;
        }

        public void setAvailableAcademicTitles(List<AcademicTitle> availableAcademicTitles) {
            this.availableAcademicTitles = availableAcademicTitles;
        }

        public boolean isLeader() {
            return isLeader;
        }

        public void setLeader(boolean isLeader) {
            this.isLeader = isLeader;
        }

        public String getBirthDate() {
            return birthDate;
        }

        public void setBirthDate(String birthDate) {
            this.birthDate = birthDate;
        }

        public Nationality getNationality() {
            return nationality;
        }

        public void setNationality(Nationality nationality) {
            this.nationality = nationality;
        }

        public String getIdentityNumber() {
            return identityNumber;
        }

        public void setIdentityNumber(String identityNumber) {
            this.identityNumber = identityNumber;
        }

        public PassportInfo getPassportInfo() {
            return passportInfo;
        }

        public void setPassportInfo(PassportInfo passportInfo) {
            this.passportInfo = passportInfo;
        }

        public Address getAddress() {
            return address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        public DestinationAddress getDestinationAddress() {
            return destinationAddress;
        }

        public void setDestinationAddress(DestinationAddress destinationAddress) {
            this.destinationAddress = destinationAddress;
        }

        public List<Service> getServices() {
            return services;
        }

        public void setServices(List<Service> services) {
            this.services = services;
        }

        public int getOrderNumber() {
            return orderNumber;
        }

        public void setOrderNumber(int orderNumber) {
            this.orderNumber = orderNumber;
        }

        public String getBirthDateFrom() {
            return birthDateFrom;
        }

        public void setBirthDateFrom(String birthDateFrom) {
            this.birthDateFrom = birthDateFrom;
        }

        public String getBirthDateTo() {
            return birthDateTo;
        }

        public void setBirthDateTo(String birthDateTo) {
            this.birthDateTo = birthDateTo;
        }

        public List<String> getRequiredFields() {
            return requiredFields;
        }

        public void setRequiredFields(List<String> requiredFields) {
            this.requiredFields = requiredFields;
        }

        public List<Document> getDocuments() {
            return documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public int getPassengerType() {
            return passengerType;
        }

        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }

        public AdditionalFields getAdditionalFields() {
            return additionalFields;
        }

        public void setAdditionalFields(AdditionalFields additionalFields) {
            this.additionalFields = additionalFields;
        }

        public List<InsertField> getInsertFields() {
            return insertFields;
        }

        public void setInsertFields(List<InsertField> insertFields) {
            this.insertFields = insertFields;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }

    public static class Title {
        private String id;
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class AcademicTitle {
        private String id;
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Nationality {
        private String twoLetterCode;

        public String getTwoLetterCode() {
            return twoLetterCode;
        }

        public void setTwoLetterCode(String twoLetterCode) {
            this.twoLetterCode = twoLetterCode;
        }
    }

    public static class PassportInfo {
        private String expireDate;
        private String issueDate;
        private String citizenshipCountryCode;

        public String getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(String expireDate) {
            this.expireDate = expireDate;
        }

        public String getIssueDate() {
            return issueDate;
        }

        public void setIssueDate(String issueDate) {
            this.issueDate = issueDate;
        }

        public String getCitizenshipCountryCode() {
            return citizenshipCountryCode;
        }

        public void setCitizenshipCountryCode(String citizenshipCountryCode) {
            this.citizenshipCountryCode = citizenshipCountryCode;
        }
    }

    public static class Address {
        private ContactPhone contactPhone;
        private String email;
        private String address;
        private String zipCode;
        private City city;
        private Country country;
        private List<String> addressLines;
        private String phone;

        public ContactPhone getContactPhone() {
            return contactPhone;
        }

        public void setContactPhone(ContactPhone contactPhone) {
            this.contactPhone = contactPhone;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getZipCode() {
            return zipCode;
        }

        public void setZipCode(String zipCode) {
            this.zipCode = zipCode;
        }

        public City getCity() {
            return city;
        }

        public void setCity(City city) {
            this.city = city;
        }

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }

        public List<String> getAddressLines() {
            return addressLines;
        }

        public void setAddressLines(List<String> addressLines) {
            this.addressLines = addressLines;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    public static class ContactPhone {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class City {
        private String id;
        private String name;
        private String code;
        private int type;
        private String latitude;
        private String longitude;
        private String parentId;
        private String countryId;
        private int provider;
        private boolean isTopRegion;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getCountryId() {
            return countryId;
        }

        public void setCountryId(String countryId) {
            this.countryId = countryId;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public boolean isTopRegion() {
            return isTopRegion;
        }

        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }
    }

    public static class Country {
        private String id;
        private String name;
        private String code;
        private String internationalCode;
        private int type;
        private String latitude;
        private String longitude;
        private String parentId;
        private String countryId;
        private int provider;
        private boolean isTopRegion;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getInternationalCode() {
            return internationalCode;
        }

        public void setInternationalCode(String internationalCode) {
            this.internationalCode = internationalCode;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getCountryId() {
            return countryId;
        }

        public void setCountryId(String countryId) {
            this.countryId = countryId;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public boolean getIsTopRegion() {
            return isTopRegion;
        }

        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }
    }

    public static class DestinationAddress {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class Service {
        private int orderNumber;
        private String note;
        private Country departureCountry;
        private City departureCity;
        private Country arrivalCountry;
        private City arrivalCity;
        private ServiceDetails serviceDetails;
        private String partnerServiceId;
        private boolean isMainService;
        private boolean isRefundable;
        private boolean bundle;
        private List<CancellationPolicy> cancellationPolicies;
        private List<Document> documents;
        private String encryptedServiceNumber;
        private List<PriceBreakDown> priceBreakDowns;
        private double commission;
        private ReservableInfo reservableInfo;
        private int unit;
        private List<ConditionalSpo> conditionalSpos;
        private int confirmationStatus;
        private int serviceStatus;
        private int productType;
        private boolean createServiceTypeIfNotExists;
        private String id;
        private String code;
        private String name;
        private String beginDate;
        private String endDate;
        private int adult;
        private int child;
        private int infant;
        private Price price;
        private boolean includePackage;
        private boolean compulsory;
        private boolean isExtraService;
        private int provider;
        private List<String> travellers;
        private boolean thirdPartyRecord;
        private int recordId;
        private AdditionalFields additionalFields;
        private int type;
        private int passengerType;

        public int getOrderNumber() {
            return orderNumber;
        }

        public void setOrderNumber(int orderNumber) {
            this.orderNumber = orderNumber;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }

        public Country getDepartureCountry() {
            return departureCountry;
        }

        public void setDepartureCountry(Country departureCountry) {
            this.departureCountry = departureCountry;
        }

        public City getDepartureCity() {
            return departureCity;
        }

        public void setDepartureCity(City departureCity) {
            this.departureCity = departureCity;
        }

        public Country getArrivalCountry() {
            return arrivalCountry;
        }

        public void setArrivalCountry(Country arrivalCountry) {
            this.arrivalCountry = arrivalCountry;
        }

        public City getArrivalCity() {
            return arrivalCity;
        }

        public void setArrivalCity(City arrivalCity) {
            this.arrivalCity = arrivalCity;
        }

        public ServiceDetails getServiceDetails() {
            return serviceDetails;
        }

        public void setServiceDetails(ServiceDetails serviceDetails) {
            this.serviceDetails = serviceDetails;
        }

        public String getPartnerServiceId() {
            return partnerServiceId;
        }

        public void setPartnerServiceId(String partnerServiceId) {
            this.partnerServiceId = partnerServiceId;
        }

        public boolean isMainService() {
            return isMainService;
        }

        public void setMainService(boolean isMainService) {
            this.isMainService = isMainService;
        }

        public boolean isRefundable() {
            return isRefundable;
        }

        public void setRefundable(boolean isRefundable) {
            this.isRefundable = isRefundable;
        }

        public boolean isBundle() {
            return bundle;
        }

        public void setBundle(boolean bundle) {
            this.bundle = bundle;
        }

        public List<CancellationPolicy> getCancellationPolicies() {
            return cancellationPolicies;
        }

        public void setCancellationPolicies(List<CancellationPolicy> cancellationPolicies) {
            this.cancellationPolicies = cancellationPolicies;
        }

        public List<Document> getDocuments() {
            return documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public String getEncryptedServiceNumber() {
            return encryptedServiceNumber;
        }

        public void setEncryptedServiceNumber(String encryptedServiceNumber) {
            this.encryptedServiceNumber = encryptedServiceNumber;
        }

        public List<PriceBreakDown> getPriceBreakDowns() {
            return priceBreakDowns;
        }

        public void setPriceBreakDowns(List<PriceBreakDown> priceBreakDowns) {
            this.priceBreakDowns = priceBreakDowns;
        }

        public double getCommission() {
            return commission;
        }

        public void setCommission(double commission) {
            this.commission = commission;
        }

        public ReservableInfo getReservableInfo() {
            return reservableInfo;
        }

        public void setReservableInfo(ReservableInfo reservableInfo) {
            this.reservableInfo = reservableInfo;
        }

        public int getUnit() {
            return unit;
        }

        public void setUnit(int unit) {
            this.unit = unit;
        }

        public List<ConditionalSpo> getConditionalSpos() {
            return conditionalSpos;
        }

        public void setConditionalSpos(List<ConditionalSpo> conditionalSpos) {
            this.conditionalSpos = conditionalSpos;
        }

        public int getConfirmationStatus() {
            return confirmationStatus;
        }

        public void setConfirmationStatus(int confirmationStatus) {
            this.confirmationStatus = confirmationStatus;
        }

        public int getServiceStatus() {
            return serviceStatus;
        }

        public void setServiceStatus(int serviceStatus) {
            this.serviceStatus = serviceStatus;
        }

        public int getProductType() {
            return productType;
        }

        public void setProductType(int productType) {
            this.productType = productType;
        }

        public boolean isCreateServiceTypeIfNotExists() {
            return createServiceTypeIfNotExists;
        }

        public void setCreateServiceTypeIfNotExists(boolean createServiceTypeIfNotExists) {
            this.createServiceTypeIfNotExists = createServiceTypeIfNotExists;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getBeginDate() {
            return beginDate;
        }

        public void setBeginDate(String beginDate) {
            this.beginDate = beginDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public int getAdult() {
            return adult;
        }

        public void setAdult(int adult) {
            this.adult = adult;
        }

        public int getChild() {
            return child;
        }

        public void setChild(int child) {
            this.child = child;
        }

        public int getInfant() {
            return infant;
        }

        public void setInfant(int infant) {
            this.infant = infant;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public boolean isIncludePackage() {
            return includePackage;
        }

        public void setIncludePackage(boolean includePackage) {
            this.includePackage = includePackage;
        }

        public boolean isCompulsory() {
            return compulsory;
        }

        public void setCompulsory(boolean compulsory) {
            this.compulsory = compulsory;
        }

        public boolean isExtraService() {
            return isExtraService;
        }

        public void setExtraService(boolean isExtraService) {
            this.isExtraService = isExtraService;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public List<String> getTravellers() {
            return travellers;
        }

        public void setTravellers(List<String> travellers) {
            this.travellers = travellers;
        }

        public boolean isThirdPartyRecord() {
            return thirdPartyRecord;
        }

        public void setThirdPartyRecord(boolean thirdPartyRecord) {
            this.thirdPartyRecord = thirdPartyRecord;
        }

        public int getRecordId() {
            return recordId;
        }

        public void setRecordId(int recordId) {
            this.recordId = recordId;
        }

        public AdditionalFields getAdditionalFields() {
            return additionalFields;
        }

        public void setAdditionalFields(AdditionalFields additionalFields) {
            this.additionalFields = additionalFields;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getPassengerType() {
            return passengerType;
        }

        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }
    }

    public static class Document {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class AdditionalFields {
        private String travellerTypeOrder;
        private String travellerUniqueID;
        private String tourVisio_TravellerId;
        private String paximum_TravellerId;
        private String birthDateFrom;
        private String birthDateTo;
        private String smsLimit;
        private String priceChanged;
        private String allowSalePriceEdit;
        private String sendFlightSms;
        private String isRefundable;
        private String reservableInfo;
        private String isEditable;

        public String getTravellerTypeOrder() {
            return travellerTypeOrder;
        }

        public void setTravellerTypeOrder(String travellerTypeOrder) {
            this.travellerTypeOrder = travellerTypeOrder;
        }

        public String getTravellerUniqueID() {
            return travellerUniqueID;
        }

        public void setTravellerUniqueID(String travellerUniqueID) {
            this.travellerUniqueID = travellerUniqueID;
        }

        public String getTourVisio_TravellerId() {
            return tourVisio_TravellerId;
        }

        public void setTourVisio_TravellerId(String tourVisio_TravellerId) {
            this.tourVisio_TravellerId = tourVisio_TravellerId;
        }

        public String getPaximum_TravellerId() {
            return paximum_TravellerId;
        }

        public void setPaximum_TravellerId(String paximum_TravellerId) {
            this.paximum_TravellerId = paximum_TravellerId;
        }

        public String getBirthDateFrom() {
            return birthDateFrom;
        }

        public void setBirthDateFrom(String birthDateFrom) {
            this.birthDateFrom = birthDateFrom;
        }

        public String getBirthDateTo() {
            return birthDateTo;
        }

        public void setBirthDateTo(String birthDateTo) {
            this.birthDateTo = birthDateTo;
        }

        public String getSmsLimit() {
            return smsLimit;
        }

        public void setSmsLimit(String smsLimit) {
            this.smsLimit = smsLimit;
        }

        public String getPriceChanged() {
            return priceChanged;
        }

        public void setPriceChanged(String priceChanged) {
            this.priceChanged = priceChanged;
        }

        public String getAllowSalePriceEdit() {
            return allowSalePriceEdit;
        }

        public void setAllowSalePriceEdit(String allowSalePriceEdit) {
            this.allowSalePriceEdit = allowSalePriceEdit;
        }

        public String getSendFlightSms() {
            return sendFlightSms;
        }

        public void setSendFlightSms(String sendFlightSms) {
            this.sendFlightSms = sendFlightSms;
        }

        public String getIsRefundable() {
            return isRefundable;
        }

        public void setIsRefundable(String isRefundable) {
            this.isRefundable = isRefundable;
        }

        public String getReservableInfo() {
            return reservableInfo;
        }

        public void setReservableInfo(String reservableInfo) {
            this.reservableInfo = reservableInfo;
        }

        public String getIsEditable() {
            return isEditable;
        }

        public void setIsEditable(String isEditable) {
            this.isEditable = isEditable;
        }
    }

    public static class InsertField {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class ReservationInfo {
        private String bookingNumber;
        private Agency agency;
        private AgencyUser agencyUser;
        private String beginDate;
        private String endDate;
        private String note;
        private Price salePrice;
        private Price supplementDiscount;
        private Price passengerEB;
        private Price agencyEB;
        private Price passengerAmountToPay;
        private Price agencyAmountToPay;
        private Price discount;
        private Price agencyBalance;
        private Price passengerBalance;
        private Commission agencyCommission;
        private Commission brokerCommission;
        private Commission agencySupplementCommission;
        private Price promotionAmount;
        private Price priceToPay;
        private Price agencyPriceToPay;
        private Price passengerPriceToPay;
        private Price totalPrice;
        private int reservationStatus;
        private int confirmationStatus;
        private int paymentStatus;
        private List<Document> documents;
        private List<OtherDocument> otherDocuments;
        private ReservableInfo reservableInfo;
        private int paymentFrom;
        private Country departureCountry;
        private City departureCity;
        private Country arrivalCountry;
        private City arrivalCity;
        private String createDate;
        private AdditionalFields additionalFields;
        private String additionalCode1;
        private String additionalCode2;
        private String additionalCode3;
        private String additionalCode4;
        private double agencyDiscount;
        private boolean hasAvailablePromotionCode;

        public String getBookingNumber() {
            return bookingNumber;
        }

        public void setBookingNumber(String bookingNumber) {
            this.bookingNumber = bookingNumber;
        }

        public Agency getAgency() {
            return agency;
        }

        public void setAgency(Agency agency) {
            this.agency = agency;
        }

        public AgencyUser getAgencyUser() {
            return agencyUser;
        }

        public void setAgencyUser(AgencyUser agencyUser) {
            this.agencyUser = agencyUser;
        }

        public String getBeginDate() {
            return beginDate;
        }

        public void setBeginDate(String beginDate) {
            this.beginDate = beginDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }

        public Price getSalePrice() {
            return salePrice;
        }

        public void setSalePrice(Price salePrice) {
            this.salePrice = salePrice;
        }

        public Price getSupplementDiscount() {
            return supplementDiscount;
        }

        public void setSupplementDiscount(Price supplementDiscount) {
            this.supplementDiscount = supplementDiscount;
        }

        public Price getPassengerEB() {
            return passengerEB;
        }

        public void setPassengerEB(Price passengerEB) {
            this.passengerEB = passengerEB;
        }

        public Price getAgencyEB() {
            return agencyEB;
        }

        public void setAgencyEB(Price agencyEB) {
            this.agencyEB = agencyEB;
        }

        public Price getPassengerAmountToPay() {
            return passengerAmountToPay;
        }

        public void setPassengerAmountToPay(Price passengerAmountToPay) {
            this.passengerAmountToPay = passengerAmountToPay;
        }

        public Price getAgencyAmountToPay() {
            return agencyAmountToPay;
        }

        public void setAgencyAmountToPay(Price agencyAmountToPay) {
            this.agencyAmountToPay = agencyAmountToPay;
        }

        public Price getDiscount() {
            return discount;
        }

        public void setDiscount(Price discount) {
            this.discount = discount;
        }

        public Price getAgencyBalance() {
            return agencyBalance;
        }

        public void setAgencyBalance(Price agencyBalance) {
            this.agencyBalance = agencyBalance;
        }

        public Price getPassengerBalance() {
            return passengerBalance;
        }

        public void setPassengerBalance(Price passengerBalance) {
            this.passengerBalance = passengerBalance;
        }

        public Commission getAgencyCommission() {
            return agencyCommission;
        }

        public void setAgencyCommission(Commission agencyCommission) {
            this.agencyCommission = agencyCommission;
        }

        public Commission getBrokerCommission() {
            return brokerCommission;
        }

        public void setBrokerCommission(Commission brokerCommission) {
            this.brokerCommission = brokerCommission;
        }

        public Commission getAgencySupplementCommission() {
            return agencySupplementCommission;
        }

        public void setAgencySupplementCommission(Commission agencySupplementCommission) {
            this.agencySupplementCommission = agencySupplementCommission;
        }

        public Price getPromotionAmount() {
            return promotionAmount;
        }

        public void setPromotionAmount(Price promotionAmount) {
            this.promotionAmount = promotionAmount;
        }

        public Price getPriceToPay() {
            return priceToPay;
        }

        public void setPriceToPay(Price priceToPay) {
            this.priceToPay = priceToPay;
        }

        public Price getAgencyPriceToPay() {
            return agencyPriceToPay;
        }

        public void setAgencyPriceToPay(Price agencyPriceToPay) {
            this.agencyPriceToPay = agencyPriceToPay;
        }

        public Price getPassengerPriceToPay() {
            return passengerPriceToPay;
        }

        public void setPassengerPriceToPay(Price passengerPriceToPay) {
            this.passengerPriceToPay = passengerPriceToPay;
        }

        public Price getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(Price totalPrice) {
            this.totalPrice = totalPrice;
        }

        public int getReservationStatus() {
            return reservationStatus;
        }

        public void setReservationStatus(int reservationStatus) {
            this.reservationStatus = reservationStatus;
        }

        public int getConfirmationStatus() {
            return confirmationStatus;
        }

        public void setConfirmationStatus(int confirmationStatus) {
            this.confirmationStatus = confirmationStatus;
        }

        public int getPaymentStatus() {
            return paymentStatus;
        }

        public void setPaymentStatus(int paymentStatus) {
            this.paymentStatus = paymentStatus;
        }

        public List<Document> getDocuments() {
            return documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public List<OtherDocument> getOtherDocuments() {
            return otherDocuments;
        }

        public void setOtherDocuments(List<OtherDocument> otherDocuments) {
            this.otherDocuments = otherDocuments;
        }

        public ReservableInfo getReservableInfo() {
            return reservableInfo;
        }

        public void setReservableInfo(ReservableInfo reservableInfo) {
            this.reservableInfo = reservableInfo;
        }

        public int getPaymentFrom() {
            return paymentFrom;
        }

        public void setPaymentFrom(int paymentFrom) {
            this.paymentFrom = paymentFrom;
        }

        public Country getDepartureCountry() {
            return departureCountry;
        }

        public void setDepartureCountry(Country departureCountry) {
            this.departureCountry = departureCountry;
        }

        public City getDepartureCity() {
            return departureCity;
        }

        public void setDepartureCity(City departureCity) {
            this.departureCity = departureCity;
        }

        public Country getArrivalCountry() {
            return arrivalCountry;
        }

        public void setArrivalCountry(Country arrivalCountry) {
            this.arrivalCountry = arrivalCountry;
        }

        public City getArrivalCity() {
            return arrivalCity;
        }

        public void setArrivalCity(City arrivalCity) {
            this.arrivalCity = arrivalCity;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }

        public AdditionalFields getAdditionalFields() {
            return additionalFields;
        }

        public void setAdditionalFields(AdditionalFields additionalFields) {
            this.additionalFields = additionalFields;
        }

        public String getAdditionalCode1() {
            return additionalCode1;
        }

        public void setAdditionalCode1(String additionalCode1) {
            this.additionalCode1 = additionalCode1;
        }

        public String getAdditionalCode2() {
            return additionalCode2;
        }

        public void setAdditionalCode2(String additionalCode2) {
            this.additionalCode2 = additionalCode2;
        }

        public String getAdditionalCode3() {
            return additionalCode3;
        }

        public void setAdditionalCode3(String additionalCode3) {
            this.additionalCode3 = additionalCode3;
        }

        public String getAdditionalCode4() {
            return additionalCode4;
        }

        public void setAdditionalCode4(String additionalCode4) {
            this.additionalCode4 = additionalCode4;
        }

        public double getAgencyDiscount() {
            return agencyDiscount;
        }

        public void setAgencyDiscount(double agencyDiscount) {
            this.agencyDiscount = agencyDiscount;
        }

        public boolean isHasAvailablePromotionCode() {
            return hasAvailablePromotionCode;
        }

        public void setHasAvailablePromotionCode(boolean hasAvailablePromotionCode) {
            this.hasAvailablePromotionCode = hasAvailablePromotionCode;
        }
    }

    public static class Agency {
        private String code;
        private String name;
        private Country country;
        private Address address;
        private boolean ownAgency;
        private boolean aceExport;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }

        public Address getAddress() {
            return address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        public boolean isOwnAgency() {
            return ownAgency;
        }

        public void setOwnAgency(boolean ownAgency) {
            this.ownAgency = ownAgency;
        }

        public boolean isAceExport() {
            return aceExport;
        }

        public void setAceExport(boolean aceExport) {
            this.aceExport = aceExport;
        }
    }

    public static class AgencyUser {
        private Office office;
        private Operator operator;
        private Market market;
        private Agency agency;
        private String name;
        private String code;

        public Office getOffice() {
            return office;
        }

        public void setOffice(Office office) {
            this.office = office;
        }

        public Operator getOperator() {
            return operator;
        }

        public void setOperator(Operator operator) {
            this.operator = operator;
        }

        public Market getMarket() {
            return market;
        }

        public void setMarket(Market market) {
            this.market = market;
        }

        public Agency getAgency() {
            return agency;
        }

        public void setAgency(Agency agency) {
            this.agency = agency;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class Office {
        private String code;
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Operator {
        private String code;
        private String name;
        private boolean agencyCanDiscountCommission;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isAgencyCanDiscountCommission() {
            return agencyCanDiscountCommission;
        }

        public void setAgencyCanDiscountCommission(boolean agencyCanDiscountCommission) {
            this.agencyCanDiscountCommission = agencyCanDiscountCommission;
        }
    }

    public static class Market {
        private String code;
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class PaymentDetail {
        private List<PaymentPlan> paymentPlan;
        private List<PaymentInfo> paymentInfo;

        public List<PaymentPlan> getPaymentPlan() {
            return paymentPlan;
        }

        public void setPaymentPlan(List<PaymentPlan> paymentPlan) {
            this.paymentPlan = paymentPlan;
        }

        public List<PaymentInfo> getPaymentInfo() {
            return paymentInfo;
        }

        public void setPaymentInfo(List<PaymentInfo> paymentInfo) {
            this.paymentInfo = paymentInfo;
        }
    }

    public static class PaymentPlan {
        private int paymentNo;
        private String dueDate;
        private Price price;
        private boolean paymentStatus;

        public int getPaymentNo() {
            return paymentNo;
        }

        public void setPaymentNo(int paymentNo) {
            this.paymentNo = paymentNo;
        }

        public String getDueDate() {
            return dueDate;
        }

        public void setDueDate(String dueDate) {
            this.dueDate = dueDate;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public boolean isPaymentStatus() {
            return paymentStatus;
        }

        public void setPaymentStatus(boolean paymentStatus) {
            this.paymentStatus = paymentStatus;
        }
    }

    public static class Price {
        private double amount;
        private String currency;
        private double percent;

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public double getPercent() {
            return percent;
        }

        public void setPercent(double percent) {
            this.percent = percent;
        }
    }

    public static class PaymentInfo {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class Invoice {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class Commission {
        private double percent;
        private double amount;
        private String currency;

        public double getPercent() {
            return percent;
        }

        public void setPercent(double percent) {
            this.percent = percent;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    public static class OtherDocument {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class ReservableInfo {
        private boolean reservable;

        public boolean isReservable() {
            return reservable;
        }

        public void setReservable(boolean reservable) {
            this.reservable = reservable;
        }
    }

    public static class ServiceDetails {
        private String serviceId;
        private String thumbnail;
        private HotelDetail hotelDetail;
        private int night;
        private String room;
        private String board;
        private String accom;
        private GeoLocation geoLocation;

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public HotelDetail getHotelDetail() {
            return hotelDetail;
        }

        public void setHotelDetail(HotelDetail hotelDetail) {
            this.hotelDetail = hotelDetail;
        }

        public int getNight() {
            return night;
        }

        public void setNight(int night) {
            this.night = night;
        }

        public String getRoom() {
            return room;
        }

        public void setRoom(String room) {
            this.room = room;
        }

        public String getBoard() {
            return board;
        }

        public void setBoard(String board) {
            this.board = board;
        }

        public String getAccom() {
            return accom;
        }

        public void setAccom(String accom) {
            this.accom = accom;
        }

        public GeoLocation getGeoLocation() {
            return geoLocation;
        }

        public void setGeoLocation(GeoLocation geoLocation) {
            this.geoLocation = geoLocation;
        }
    }

    public static class HotelDetail {
        private Address address;
        private Location transferLocation;
        private int stopSaleGuaranteed;
        private int stopSaleStandart;
        private GeoLocation geolocation;
        private Location location;
        private Country country;
        private City city;
        private String thumbnail;
        private String id;
        private String name;

        public Address getAddress() {
            return address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        public Location getTransferLocation() {
            return transferLocation;
        }

        public void setTransferLocation(Location transferLocation) {
            this.transferLocation = transferLocation;
        }

        public int getStopSaleGuaranteed() {
            return stopSaleGuaranteed;
        }

        public void setStopSaleGuaranteed(int stopSaleGuaranteed) {
            this.stopSaleGuaranteed = stopSaleGuaranteed;
        }

        public int getStopSaleStandart() {
            return stopSaleStandart;
        }

        public void setStopSaleStandart(int stopSaleStandart) {
            this.stopSaleStandart = stopSaleStandart;
        }

        public GeoLocation getGeolocation() {
            return geolocation;
        }

        public void setGeolocation(GeoLocation geolocation) {
            this.geolocation = geolocation;
        }

        public Location getLocation() {
            return location;
        }

        public void setLocation(Location location) {
            this.location = location;
        }

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }

        public City getCity() {
            return city;
        }

        public void setCity(City city) {
            this.city = city;
        }

        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class GeoLocation {
        private String longitude;
        private String latitude;

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }
    }

    public static class Location {
        private String code;
        private String name;
        private int type;
        private String latitude;
        private String longitude;
        private String parentId;
        private String countryId;
        private int provider;
        private boolean isTopRegion;
        private String id;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getCountryId() {
            return countryId;
        }

        public void setCountryId(String countryId) {
            this.countryId = countryId;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public boolean isTopRegion() {
            return isTopRegion;
        }

        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static class CancellationPolicy {
        private String beginDate;
        private String dueDate;
        private Price price;
        private int provider;

        public String getBeginDate() {
            return beginDate;
        }

        public void setBeginDate(String beginDate) {
            this.beginDate = beginDate;
        }

        public String getDueDate() {
            return dueDate;
        }

        public void setDueDate(String dueDate) {
            this.dueDate = dueDate;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }
    }

    public static class PriceBreakDown {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }

    public static class ConditionalSpo {
        // Champs vides dans le JSON, peut être étendu si nécessaire
    }
}