<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="__XCIMB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="__XCIMR04EfCdtPSiNz5iWQ" bindingContexts="__XL71B04EfCdtPSiNz5iWQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/BookingController.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/BookingController.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingTransactionRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/BookingService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/BookingService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingTransactionResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/BookingClient.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/BookingClient.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeginTransactionRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CommitTransactionResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CommitTransactionRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;List.class&quot; tooltip=&quot;java.util.List&quot;>&#xD;&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=Paximum--Air/C:\/Users\/<USER>\/Downloads\/sts-4.27.0.RELEASE\/plugins\/org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.5.v20241023-1957\/jre\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util(List.class&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SetReservationInfoResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SetReservationInfoRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeginTransactionResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AddServicesResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/AuthController.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/AuthController.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CancelReservationRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/CancelReservationRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/CancelReservationRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationListRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;RemoveServicesRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationListResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;JacksonConfig.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/JacksonConfig.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/JacksonConfig.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetOffersRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetPaymentListRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetOffersResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;productController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/productController.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/productController.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponsePService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponsePService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponsePService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;productClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/productClient.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/productClient.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;productService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/productService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/productService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;Paximum--Air/src/main/resources/application.properties&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/resources/application.properties&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;PriceSearchRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/PriceSearchRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/PriceSearchRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Body_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Body_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Body_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Header.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Header.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Header.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;PriceSearchResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/PriceSearchResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/PriceSearchResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FlightBrandInfo_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/FlightBrandInfo_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/FlightBrandInfo_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FlightClass_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/FlightClass_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/FlightClass_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;flightBrandInfo.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/flightBrandInfo.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/flightBrandInfo.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FlightProvider_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/FlightProvider_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/FlightProvider_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/AuthClient.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/AuthClient.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/CorsConfig.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/CorsConfig.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;WebConfig.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/WebConfig.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/WebConfig.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/priceMethodeController.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/priceMethodeController.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AddServicesRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetPaymentListResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationDetailResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationDetailRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;String.class&quot; tooltip=&quot;java.lang.String&quot;>&#xD;&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=Paximum--Air/C:\/Users\/<USER>\/Downloads\/sts-4.27.0.RELEASE\/plugins\/org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.5.v20241023-1957\/jre\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.lang(String.class&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationListRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/GetReservationListRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/GetReservationListRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ReservationInfoRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/ReservationInfoRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/ReservationInfoRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Service_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Service_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Service_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;RemoveServicesResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/priceMethodeService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/priceMethodeService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/AuthService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/AuthService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/AuthRequest.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/AuthRequest.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Body.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Body.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Body.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Market.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Market.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Market.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/priceMethodeClient.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/priceMethodeClient.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaggageInformation_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BaggageInformation_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BaggageInformation_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Agency.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Agency.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Agency.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Airline_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Airline_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Airline_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FlightProvider_Pservice.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/FlightProvider_Pservice.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/FlightProvider_Pservice.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Header_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Header_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Header_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Segment_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Segment_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Segment_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeoLocation_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GeoLocation_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GeoLocation_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Airport_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Airport_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Airport_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;City_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/City_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/City_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Country_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Country_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Country_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;LocationInfo_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/LocationInfo_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/LocationInfo_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Passenger_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Passenger_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Passenger_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Item_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Item_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Item_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Flight_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Flight_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Flight_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Message_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Message_PService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Message_PService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FlightProviderPservice.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/FlightProviderPservice.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/FlightProviderPservice.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ItemPService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/ItemPService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/ItemPService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FlightPService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/FlightPService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/FlightPService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BodyPService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BodyPService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BodyPService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MessagePService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/MessagePService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/MessagePService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;HeaderPService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/HeaderPService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/HeaderPService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;Paximum--Air/pom.xml&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/pom.xml&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/controllers/priceMethodeController.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/controllers/priceMethodeController.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/services/priceMethodeService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/services/priceMethodeService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/config/priceMethodeClient.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/config/priceMethodeClient.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponsePService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponsePService.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponsePService.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponse.java&quot; tooltip=&quot;product_service/src/main/java/com/paximum/productservice/models/ApiResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/product_service/src/main/java/com/paximum/productservice/models/ApiResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/config/AuthClient.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/config/AuthClient.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductServiceApplication.java&quot; tooltip=&quot;product_service/src/main/java/com/paximum/productservice/ProductServiceApplication.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/product_service/src/main/java/com/paximum/productservice/ProductServiceApplication.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Market.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Market.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Market.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Operator.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Operator.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Operator.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Office.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Office.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Office.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Agency.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Agency.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Agency.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MainAgency.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/MainAgency.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/MainAgency.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;UserInfo.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/UserInfo.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/UserInfo.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Body.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Body.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Body.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Message.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Message.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Message.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Header.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/Header.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/Header.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponse.java&quot; tooltip=&quot;authentication_service/src/main/java/com/paximum/auth/models/ApiResponse.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/authentication_service/src/main/java/com/paximum/auth/models/ApiResponse.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponseBody.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponseBody.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponseBody.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponseHeader.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponseHeader.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/models/ApiResponseHeader.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;authentication_service/src/main/resources/application.properties&quot;>&#xD;&#xA;&lt;persistable path=&quot;/authentication_service/src/main/resources/application.properties&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;PaximumAirApplication.java&quot; tooltip=&quot;Paximum-------------Air/src/main/java/com/paximum/auth/PaximumAirApplication.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum-------------Air/src/main/java/com/paximum/auth/PaximumAirApplication.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;PaximumAirApplication.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/auth/PaximumAirApplication.java&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Paximum--Air/src/main/java/com/paximum/auth/PaximumAirApplication.java&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="__XCIMR04EfCdtPSiNz5iWQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="__XCIMh04EfCdtPSiNz5iWQ" label="%trimmedwindow.label.eclipseSDK" x="0" y="0" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1740488746445"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="__XCIMh04EfCdtPSiNz5iWQ" selectedElement="__XCIMx04EfCdtPSiNz5iWQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="__XCIMx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="__XCINB04EfCdtPSiNz5iWQ">
        <children xsi:type="advanced:Perspective" xmi:id="__XCINB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="__XCINR04EfCdtPSiNz5iWQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$Ctrl+Shift+T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="__XCINR04EfCdtPSiNz5iWQ" selectedElement="__XCIQR04EfCdtPSiNz5iWQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="__XCINh04EfCdtPSiNz5iWQ" containerData="2500" selectedElement="__XCINx04EfCdtPSiNz5iWQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="__XCINx04EfCdtPSiNz5iWQ" containerData="6000" selectedElement="__XCIOB04EfCdtPSiNz5iWQ">
                <children xsi:type="basic:PartStack" xmi:id="__XCIOB04EfCdtPSiNz5iWQ" elementId="left" containerData="6600" selectedElement="__XCIOR04EfCdtPSiNz5iWQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <tags>active</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIOR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="__XCKlR04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIOh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="__XL5OB04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIOx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="__XL5OR04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIPB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="__XL5iB04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="__XCIPR04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" toBeRendered="false" containerData="3400">
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIPh04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="__XL5ix04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="__XCIPx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="__XCIQB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="__XL5iR04EfCdtPSiNz5iWQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="__XCIQR04EfCdtPSiNz5iWQ" containerData="7500" selectedElement="__XCIQh04EfCdtPSiNz5iWQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="__XCIQh04EfCdtPSiNz5iWQ" containerData="6042" selectedElement="__XCIRB04EfCdtPSiNz5iWQ">
                <children xsi:type="advanced:Placeholder" xmi:id="__XCIQx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editorss" containerData="7412" ref="__XCKYB04EfCdtPSiNz5iWQ"/>
                <children xsi:type="basic:PartStack" xmi:id="__XCIRB04EfCdtPSiNz5iWQ" elementId="bottom" containerData="2588" selectedElement="__XCISR04EfCdtPSiNz5iWQ">
                  <tags>org.eclipse.e4.secondaryDataStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIRR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProblemView" ref="__XL5Oh04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIRh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="__XL5QB04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIRx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.SourceView" ref="__XL5Qx04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCISB04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="__XL5Rh04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCISR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView" ref="__XL5Rx04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCISh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="__XL5Wh04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCISx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="__XL5Wx04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCITB04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" ref="__XL5jh04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="__XCITR04EfCdtPSiNz5iWQ" containerData="1458" selectedElement="__XCIUB04EfCdtPSiNz5iWQ">
                <children xsi:type="basic:PartStack" xmi:id="__XCITh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="__XCITx04EfCdtPSiNz5iWQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCITx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="__XL5YR04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="__XCIUB04EfCdtPSiNz5iWQ" elementId="right" containerData="5000" selectedElement="__XCIVh04EfCdtPSiNz5iWQ">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>General</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIUR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ContentOutline" ref="__XL5XB04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIUh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="__XL5Xx04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIUx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="__XL5YB04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIVB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="__XL5ih04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIVR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="__XL5kR04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Ant</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="__XCIVh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.LogView" ref="__XL5kh04EfCdtPSiNz5iWQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="__XCIVx04EfCdtPSiNz5iWQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="__XCIWB04EfCdtPSiNz5iWQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="__XCKXR04EfCdtPSiNz5iWQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="__XCIWR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="__XCKXh04EfCdtPSiNz5iWQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="__XCIWh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="__XCKXx04EfCdtPSiNz5iWQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="__XCKXR04EfCdtPSiNz5iWQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XCKXh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XCKXx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="__XCKYB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editorss" selectedElement="__XCKYR04EfCdtPSiNz5iWQ">
      <children xsi:type="basic:PartStack" xmi:id="__XCKYR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="__XCKax04EfCdtPSiNz5iWQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <children xsi:type="basic:Part" xmi:id="__XCKYh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BeginTransactionResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeginTransactionResponse.java&quot; partName=&quot;BeginTransactionResponse.java&quot; title=&quot;BeginTransactionResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;62767&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKYx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Header.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Header.java&quot; partName=&quot;Header.java&quot; title=&quot;Header.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Header.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Header.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;589&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKZB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ApiResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponse.java&quot; partName=&quot;ApiResponse.java&quot; title=&quot;ApiResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;98&quot; selectionOffset=&quot;36&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKZR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Body.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Body.java&quot; partName=&quot;Body.java&quot; title=&quot;Body.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Body.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Body.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;849&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKZh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BookingService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingService.java&quot; partName=&quot;BookingService.java&quot; title=&quot;BookingService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/BookingService.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/BookingService.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1367&quot; selectionTopPixel=&quot;160&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKah04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="AuthController.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthController.java&quot; partName=&quot;AuthController.java&quot; title=&quot;AuthController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/AuthController.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/AuthController.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;1039&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKax04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BookingController.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingController.java&quot; partName=&quot;BookingController.java&quot; title=&quot;BookingController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/BookingController.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/BookingController.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;17&quot; selectionOffset=&quot;369&quot; selectionTopPixel=&quot;231&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKbx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BookingClient.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingClient.java&quot; partName=&quot;BookingClient.java&quot; title=&quot;BookingClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/BookingClient.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/BookingClient.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1718&quot; selectionTopPixel=&quot;11&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKcB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="AuthClient.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthClient.java&quot; partName=&quot;AuthClient.java&quot; title=&quot;AuthClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/AuthClient.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/AuthClient.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;813&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKcR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="AuthRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthRequest.java&quot; partName=&quot;AuthRequest.java&quot; title=&quot;AuthRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/AuthRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/AuthRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;585&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKch04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="AuthService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AuthService.java&quot; partName=&quot;AuthService.java&quot; title=&quot;AuthService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/AuthService.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/AuthService.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;702&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKcx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="PriceSearchRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;PriceSearchRequest.java&quot; partName=&quot;PriceSearchRequest.java&quot; title=&quot;PriceSearchRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/PriceSearchRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/PriceSearchRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;7828&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKdB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="priceMethodeService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;priceMethodeService.java&quot; partName=&quot;priceMethodeService.java&quot; title=&quot;priceMethodeService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/priceMethodeService.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/priceMethodeService.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;780&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKdR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BeginTransactionRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeginTransactionRequest.java&quot; partName=&quot;BeginTransactionRequest.java&quot; title=&quot;BeginTransactionRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BeginTransactionRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1784&quot; selectionTopPixel=&quot;799&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKdh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="AddServicesRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AddServicesRequest.java&quot; partName=&quot;AddServicesRequest.java&quot; title=&quot;AddServicesRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;1754&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKdx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="AddServicesResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AddServicesResponse.java&quot; partName=&quot;AddServicesResponse.java&quot; title=&quot;AddServicesResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/AddServicesResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;2&quot; selectionOffset=&quot;43796&quot; selectionTopPixel=&quot;16100&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKeB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="RemoveServicesRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;RemoveServicesRequest.java&quot; partName=&quot;RemoveServicesRequest.java&quot; title=&quot;RemoveServicesRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;1&quot; selectionOffset=&quot;715&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKeR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="RemoveServicesResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;RemoveServicesResponse.java&quot; partName=&quot;RemoveServicesResponse.java&quot; title=&quot;RemoveServicesResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/RemoveServicesResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;657&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKeh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SetReservationInfoResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SetReservationInfoResponse.java&quot; partName=&quot;SetReservationInfoResponse.java&quot; title=&quot;SetReservationInfoResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;784&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKex04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Service_PService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;Service_PService.java&quot; partName=&quot;Service_PService.java&quot; title=&quot;Service_PService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/Service_PService.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/Service_PService.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKfB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="CommitTransactionRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CommitTransactionRequest.java&quot; partName=&quot;CommitTransactionRequest.java&quot; title=&quot;CommitTransactionRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;4011&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKfR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BookingTransactionRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingTransactionRequest.java&quot; partName=&quot;BookingTransactionRequest.java&quot; title=&quot;BookingTransactionRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKgR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="CommitTransactionResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CommitTransactionResponse.java&quot; partName=&quot;CommitTransactionResponse.java&quot; title=&quot;CommitTransactionResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/CommitTransactionResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;2863&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKgh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetReservationDetailRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationDetailRequest.java&quot; partName=&quot;GetReservationDetailRequest.java&quot; title=&quot;GetReservationDetailRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;632&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKgx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetReservationDetailResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationDetailResponse.java&quot; partName=&quot;GetReservationDetailResponse.java&quot; title=&quot;GetReservationDetailResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationDetailResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;51793&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKhB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetReservationListRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationListRequest.java&quot; partName=&quot;GetReservationListRequest.java&quot; title=&quot;GetReservationListRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2207&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKhR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetReservationListResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetReservationListResponse.java&quot; partName=&quot;GetReservationListResponse.java&quot; title=&quot;GetReservationListResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetReservationListResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3954&quot; selectionTopPixel=&quot;6139&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKhh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetPaymentListRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetPaymentListRequest.java&quot; partName=&quot;GetPaymentListRequest.java&quot; title=&quot;GetPaymentListRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2913&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKhx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetPaymentListResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetPaymentListResponse.java&quot; partName=&quot;GetPaymentListResponse.java&quot; title=&quot;GetPaymentListResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetPaymentListResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;1&quot; selectionOffset=&quot;11812&quot; selectionTopPixel=&quot;3440&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKiB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="productController.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;productController.java&quot; partName=&quot;productController.java&quot; title=&quot;productController.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/controllers/productController.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/controllers/productController.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1161&quot; selectionTopPixel=&quot;59&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKiR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="productService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;productService.java&quot; partName=&quot;productService.java&quot; title=&quot;productService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/services/productService.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/services/productService.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1124&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKih04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="WebConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;WebConfig.java&quot; partName=&quot;WebConfig.java&quot; title=&quot;WebConfig.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/WebConfig.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/WebConfig.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKix04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="productClient.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;productClient.java&quot; partName=&quot;productClient.java&quot; title=&quot;productClient.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/productClient.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/productClient.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;1654&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;119&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKjB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="CorsConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; partName=&quot;CorsConfig.java&quot; title=&quot;CorsConfig.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/config/CorsConfig.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/config/CorsConfig.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKjR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetOffersRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetOffersRequest.java&quot; partName=&quot;GetOffersRequest.java&quot; title=&quot;GetOffersRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1158&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKjh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GetOffersResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GetOffersResponse.java&quot; partName=&quot;GetOffersResponse.java&quot; title=&quot;GetOffersResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/GetOffersResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;11726&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKjx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ApiResponsePService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ApiResponsePService.java&quot; partName=&quot;ApiResponsePService.java&quot; title=&quot;ApiResponsePService.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponsePService.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/ApiResponsePService.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;114&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKkB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="application.properties" iconURI="platform:/plugin/org.springframework.tooling.boot.ls/icons/spring_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; partName=&quot;application.properties&quot; title=&quot;application.properties&quot; tooltip=&quot;Paximum--Air/src/main/resources/application.properties&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/resources/application.properties&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;53&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>SpringBootPropertyEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKkR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SetReservationInfoRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SetReservationInfoRequest.java&quot; partName=&quot;SetReservationInfoRequest.java&quot; title=&quot;SetReservationInfoRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/SetReservationInfoRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;11261&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKkh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BookingTransactionResponse.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BookingTransactionResponse.java&quot; partName=&quot;BookingTransactionResponse.java&quot; title=&quot;BookingTransactionResponse.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionResponse.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/BookingTransactionResponse.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1268&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKkx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="CancelReservationRequest.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CancelReservationRequest.java&quot; partName=&quot;CancelReservationRequest.java&quot; title=&quot;CancelReservationRequest.java&quot; tooltip=&quot;Paximum--Air/src/main/java/com/paximum/demo/models/CancelReservationRequest.java&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Paximum--Air/src/main/java/com/paximum/demo/models/CancelReservationRequest.java&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;791&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="__XCKlB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="List.class" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/classf_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;List.class&quot; partName=&quot;List.class&quot; title=&quot;List.class&quot; tooltip=&quot;java.util.List&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=Paximum--Air/C:\/Users\/<USER>\/Downloads\/sts-4.27.0.RELEASE\/plugins\/org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.5.v20241023-1957\/jre\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util(List.class&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;50786&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;2140&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.ClassFileEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XCKlR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xD;&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xD;&#xA;&lt;xmlDefinedFilters>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/xmlDefinedFilters>&#xD;&#xA;&lt;/customFilters>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>active</tags>
      <menus xmi:id="__XCKlh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5NB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.PackageExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5OB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5OR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Oh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;120&quot; org.eclipse.ui.ide.markerType=&quot;120&quot; org.eclipse.ui.ide.pathField=&quot;160&quot; org.eclipse.ui.ide.resourceField=&quot;120&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;400&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="__XL5Ox04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5PR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5QB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="__XL5QR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavadocView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5Qh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavadocView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Qx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="__XL5RB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.SourceView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5RR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.SourceView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Rh04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Rx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="__XL5SB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5Sh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Wh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5XB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="__XL5XR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5Xh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5Xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5YB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5YR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xD;&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xD;&#xA;&lt;sorter>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;filteredTreeFindHistory>&#xD;&#xA;&lt;historyItem text=&quot;get&quot;/>&#xD;&#xA;&lt;historyItem text=&quot;fli&quot;/>&#xD;&#xA;&lt;/filteredTreeFindHistory>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="__XL5Yh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5eR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5iB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5iR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5ih04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5ix04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="__XL5jB04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5jR04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5jh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;terminalConnections id=&quot;org.eclipse.tm.terminal.view.ui.TerminalsView&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
      <menus xmi:id="__XL5jx04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5kB04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5kR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__XL5kh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view activate=&quot;false&quot; activateError=&quot;true&quot; activateWarn=&quot;false&quot; allSessions=&quot;true&quot; column2=&quot;300&quot; column3=&quot;150&quot; column4=&quot;150&quot; error=&quot;true&quot; groupBy=&quot;0&quot; info=&quot;true&quot; limit=&quot;50&quot; maxLogTailSize=&quot;1&quot; ok=&quot;true&quot; orderType=&quot;2&quot; orderValue=&quot;-1&quot; show_filter_text=&quot;true&quot; useLimit=&quot;true&quot; warning=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="__XL5kx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.LogView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__XL5oR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.LogView"/>
    </sharedElements>
    <trimBars xmi:id="__XL5qx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="__XL5rB04EfCdtPSiNz5iWQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="__XL5rR04EfCdtPSiNz5iWQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5rh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="__XL5th04EfCdtPSiNz5iWQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="__XPBMx04EfCdtPSiNz5iWQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5ux04EfCdtPSiNz5iWQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="__XL5vB04EfCdtPSiNz5iWQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5vR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="__XL5vx04EfCdtPSiNz5iWQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="__XO_ax04EfCdtPSiNz5iWQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="__XL5wB04EfCdtPSiNz5iWQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="__XO_6B04EfCdtPSiNz5iWQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5wR04EfCdtPSiNz5iWQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="__XL5wh04EfCdtPSiNz5iWQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL50x04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL52h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL53x04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL55B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL57h04EfCdtPSiNz5iWQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="__XL57x04EfCdtPSiNz5iWQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL58B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="__XL59h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="__XPA7B04EfCdtPSiNz5iWQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5-x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5_B04EfCdtPSiNz5iWQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="__XL5_R04EfCdtPSiNz5iWQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL5_h04EfCdtPSiNz5iWQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="__XL5_x04EfCdtPSiNz5iWQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="__XL6AB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Ax04EfCdtPSiNz5iWQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Bx04EfCdtPSiNz5iWQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="__XL6Dh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Dx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6EB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6ER04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="__XL6FR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Fh04EfCdtPSiNz5iWQ" elementId="left(IDEWindow).(org.eclipse.jdt.ui.JavaPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Fx04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack(IDEWindow).(org.eclipse.jdt.ui.JavaPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6GB04EfCdtPSiNz5iWQ" elementId="bottom(IDEWindow).(org.eclipse.jdt.ui.JavaPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="__XL6GR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Gh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack(IDEWindow).(org.eclipse.jdt.ui.JavaPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6Gx04EfCdtPSiNz5iWQ" elementId="right(IDEWindow).(org.eclipse.jdt.ui.JavaPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="__XL6HB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.jdt.ui.JavaPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="__XL6HR04EfCdtPSiNz5iWQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="__XL71B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL6Hh04EfCdtPSiNz5iWQ" keySequence="CTRL+1" command="__XO_Kx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Hx04EfCdtPSiNz5iWQ" keySequence="CTRL+6" command="__XO_VB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6IB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+I" command="__XO_Cx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6IR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+L" command="__XPBax04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Ih04EfCdtPSiNz5iWQ" keySequence="CTRL+SPACE" command="__XPBCx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Ix04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+D" command="__XPBlB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6JB04EfCdtPSiNz5iWQ" keySequence="CTRL+V" command="__XO-jh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6JR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+SPACE" command="__XO_SB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Jh04EfCdtPSiNz5iWQ" keySequence="CTRL+A" command="__XO_wB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Jx04EfCdtPSiNz5iWQ" keySequence="CTRL+C" command="__XPAHh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6KB04EfCdtPSiNz5iWQ" keySequence="CTRL+X" command="__XO_cR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6KR04EfCdtPSiNz5iWQ" keySequence="CTRL+Y" command="__XO_6B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Kh04EfCdtPSiNz5iWQ" keySequence="CTRL+Z" command="__XO_ax04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Kx04EfCdtPSiNz5iWQ" keySequence="ALT+PAGE_UP" command="__XO_-B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6LB04EfCdtPSiNz5iWQ" keySequence="ALT+PAGE_DOWN" command="__XPAuR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6LR04EfCdtPSiNz5iWQ" keySequence="SHIFT+INSERT" command="__XO-jh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Lh04EfCdtPSiNz5iWQ" keySequence="ALT+F11" command="__XO-zx04EfCdtPSiNz5iWQ">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="__XL6Lx04EfCdtPSiNz5iWQ" keySequence="CTRL+F10" command="__XO-rh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6MB04EfCdtPSiNz5iWQ" keySequence="CTRL+INSERT" command="__XPAHh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6MR04EfCdtPSiNz5iWQ" keySequence="CTRL+PAGE_UP" command="__XPBTB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Mh04EfCdtPSiNz5iWQ" keySequence="CTRL+PAGE_DOWN" command="__XO_NB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Mx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+F1" command="__XO-5h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6NB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+F2" command="__XPAoR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6NR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+F3" command="__XPBQB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Nh04EfCdtPSiNz5iWQ" keySequence="SHIFT+DEL" command="__XO_cR04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL6Nx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="__XL72R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL6OB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+CR" command="__XPBPx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6OR04EfCdtPSiNz5iWQ" keySequence="CTRL+BS" command="__XO-Yx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Oh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+Q" command="__XO_ER04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Ox04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+J" command="__XO_Bh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6PB04EfCdtPSiNz5iWQ" keySequence="CTRL++" command="__XPAlh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6PR04EfCdtPSiNz5iWQ" keySequence="CTRL+-" command="__XO_oh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Ph04EfCdtPSiNz5iWQ" keySequence="CTRL+/" command="__XO-bh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Px04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+C" command="__XO-dR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6QB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+C" command="__XO-bh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6QR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F" command="__XO_Ux04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Qh04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+J" command="__XO_Ih04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Qx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+A" command="__XPAQB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6RB04EfCdtPSiNz5iWQ" keySequence="CTRL+T" command="__XPBqh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6RR04EfCdtPSiNz5iWQ" keySequence="CTRL+J" command="__XO-tB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Rh04EfCdtPSiNz5iWQ" keySequence="CTRL+L" command="__XPBHx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Rx04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XPAJB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6SB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+/" command="__XO_XR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6SR04EfCdtPSiNz5iWQ" keySequence="CTRL+D" command="__XO-wB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Sh04EfCdtPSiNz5iWQ" keySequence="CTRL+=" command="__XPAlh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Sx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Y" command="__XO-Wh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6TB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+DEL" command="__XPBEB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6TR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+X" command="__XPAKB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Th04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+Y" command="__XO_nx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Tx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+\" command="__XPAWB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6UB04EfCdtPSiNz5iWQ" keySequence="CTRL+DEL" command="__XO_Yx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6UR04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_UP" command="__XPBzx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Uh04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_DOWN" command="__XPAxR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Ux04EfCdtPSiNz5iWQ" keySequence="SHIFT+END" command="__XO_qx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6VB04EfCdtPSiNz5iWQ" keySequence="SHIFT+HOME" command="__XO_kh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6VR04EfCdtPSiNz5iWQ" keySequence="END" command="__XPBWR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Vh04EfCdtPSiNz5iWQ" keySequence="INSERT" command="__XPAYx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Vx04EfCdtPSiNz5iWQ" keySequence="F2" command="__XO_Nh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6WB04EfCdtPSiNz5iWQ" keySequence="HOME" command="__XPBdx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6WR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+ARROW_UP" command="__XPBrB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Wh04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+ARROW_DOWN" command="__XO_yR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Wx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+INSERT" command="__XO-9x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6XB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="__XO_rh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6XR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="__XO-_h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Xh04EfCdtPSiNz5iWQ" keySequence="CTRL+F10" command="__XPBOR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Xx04EfCdtPSiNz5iWQ" keySequence="CTRL+END" command="__XPAyR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6YB04EfCdtPSiNz5iWQ" keySequence="CTRL+ARROW_UP" command="__XO-5B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6YR04EfCdtPSiNz5iWQ" keySequence="CTRL+ARROW_DOWN" command="__XPB4B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Yh04EfCdtPSiNz5iWQ" keySequence="CTRL+ARROW_LEFT" command="__XPAGB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Yx04EfCdtPSiNz5iWQ" keySequence="CTRL+ARROW_RIGHT" command="__XO_EB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ZB04EfCdtPSiNz5iWQ" keySequence="CTRL+HOME" command="__XO-jR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ZR04EfCdtPSiNz5iWQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="__XPA2R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Zh04EfCdtPSiNz5iWQ" keySequence="CTRL+NUMPAD_ADD" command="__XPBmh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6Zx04EfCdtPSiNz5iWQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="__XPBPR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6aB04EfCdtPSiNz5iWQ" keySequence="CTRL+NUMPAD_DIVIDE" command="__XO-6B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6aR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="__XPA3x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ah04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="__XPAaR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ax04EfCdtPSiNz5iWQ" keySequence="ALT+/" command="__XPBeh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6bB04EfCdtPSiNz5iWQ" keySequence="SHIFT+CR" command="__XPBdh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL6bR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.contexts.window" bindingContext="__XL71R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL6bh04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+A" command="__XPAxx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6bx04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+T" command="__XO-rR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6cB04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+L" command="__XPAVx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6cR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+M" command="__XPBih04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ch04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q O" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6cx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="__XL6dB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q P" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6dR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="__XL6dh04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+B" command="__XPAsh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6dx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+R" command="__XPB5B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6eB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q Q" command="__XPAqh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6eR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+S" command="__XPAih04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6eh04EfCdtPSiNz5iWQ" keySequence="CTRL+3" command="__XO_NR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ex04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+T" command="__XO_cB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6fB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+T" command="__XO-6x04EfCdtPSiNz5iWQ">
      <tags>deleted</tags>
    </bindings>
    <bindings xmi:id="__XL6fR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q S" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6fh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="__XL6fx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+U" command="__XO-9h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6gB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q T" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6gR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="__XL6gh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+V" command="__XPBXB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6gx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q V" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6hB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="__XL6hR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+G" command="__XPAnx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6hh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+W" command="__XO_bx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6hx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+H" command="__XPAEh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6iB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q H" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6iR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="__XL6ih04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q J" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6ix04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="__XL6jB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+K" command="__XO-4R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6jR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q K" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6jh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="__XL6jx04EfCdtPSiNz5iWQ" keySequence="CTRL+," command="__XO-kR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6kB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q L" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6kR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="__XL6kh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+N" command="__XPAMx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6kx04EfCdtPSiNz5iWQ" keySequence="CTRL+." command="__XPBsB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6lB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+O" command="__XPBjh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6lR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+P" command="__XO-0x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6lh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+A" command="__XPAtx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6lx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+B" command="__XO-4h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6mB04EfCdtPSiNz5iWQ" keySequence="CTRL+#" command="__XO-rx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6mR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+T" command="__XPAGR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6mh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+E" command="__XO-8R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6mx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+G" command="__XPBvR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6nB04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+H" command="__XO-ph04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6nR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q X" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6nh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="__XL6nx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q Y" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6oB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="__XL6oR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q Z" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6oh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="__XL6ox04EfCdtPSiNz5iWQ" keySequence="CTRL+P" command="__XPBMx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6pB04EfCdtPSiNz5iWQ" keySequence="CTRL+Q" command="__XPBRh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6pR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+C" command="__XPBDx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ph04EfCdtPSiNz5iWQ" keySequence="CTRL+S" command="__XO_pB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6px04EfCdtPSiNz5iWQ" keySequence="CTRL+U" command="__XO_4x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6qB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+F" command="__XPBSh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6qR04EfCdtPSiNz5iWQ" keySequence="CTRL+W" command="__XO_8h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6qh04EfCdtPSiNz5iWQ" keySequence="CTRL+H" command="__XPBCh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6qx04EfCdtPSiNz5iWQ" keySequence="CTRL+K" command="__XPAtB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6rB04EfCdtPSiNz5iWQ" keySequence="CTRL+M" command="__XPBBh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6rR04EfCdtPSiNz5iWQ" keySequence="CTRL+N" command="__XPBwh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6rh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+P" command="__XPAox04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6rx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+R" command="__XO_7R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6sB04EfCdtPSiNz5iWQ" keySequence="CTRL+B" command="__XO-lB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6sR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q B" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6sh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="__XL6sx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+S" command="__XPABx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6tB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+T" command="__XPAQx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6tR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q C" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6th04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="__XL6tx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Q D" command="__XPAqh04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6uB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="__XL6uR04EfCdtPSiNz5iWQ" keySequence="CTRL+E" command="__XO_YB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6uh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+V" command="__XO_rR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6ux04EfCdtPSiNz5iWQ" keySequence="CTRL+F" command="__XO-yB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6vB04EfCdtPSiNz5iWQ" keySequence="CTRL+G" command="__XO-ZB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6vR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+W" command="__XPBqx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6vh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+H" command="__XO_Wh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6vx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+I" command="__XO-sB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6wB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+J" command="__XO_Xh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6wR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+K" command="__XO_px04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6wh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+L" command="__XO_Jx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6wx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+M" command="__XPBmx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6xB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+N" command="__XO_bB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6xR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+Z" command="__XPADB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6xh04EfCdtPSiNz5iWQ" keySequence="CTRL+_" command="__XO_Sx04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6xx04EfCdtPSiNz5iWQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="__XL6yB04EfCdtPSiNz5iWQ" keySequence="CTRL+{" command="__XO_Sx04EfCdtPSiNz5iWQ">
      <parameters xmi:id="__XL6yR04EfCdtPSiNz5iWQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="__XL6yh04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_LEFT" command="__XO-sh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6yx04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_RIGHT" command="__XO_gh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6zB04EfCdtPSiNz5iWQ" keySequence="SHIFT+F2" command="__XPAeh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6zR04EfCdtPSiNz5iWQ" keySequence="SHIFT+F5" command="__XO_zx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6zh04EfCdtPSiNz5iWQ" keySequence="ALT+F7" command="__XPASh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6zx04EfCdtPSiNz5iWQ" keySequence="ALT+F5" command="__XO_uR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL60B04EfCdtPSiNz5iWQ" keySequence="F11" command="__XPBhx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL60R04EfCdtPSiNz5iWQ" keySequence="F12" command="__XPBDR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL60h04EfCdtPSiNz5iWQ" keySequence="CTRL+&#xc8;" command="__XO_Sx04EfCdtPSiNz5iWQ">
      <tags>locale:fr</tags>
      <parameters xmi:id="__XL60x04EfCdtPSiNz5iWQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="__XL61B04EfCdtPSiNz5iWQ" keySequence="F2" command="__XO-kx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL61R04EfCdtPSiNz5iWQ" keySequence="F3" command="__XO_JR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL61h04EfCdtPSiNz5iWQ" keySequence="F4" command="__XO-nB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL61x04EfCdtPSiNz5iWQ" keySequence="F5" command="__XO_ix04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL62B04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F7" command="__XPBix04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL62R04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F8" command="__XO_Sh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL62h04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F9" command="__XO_th04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL62x04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+ARROW_LEFT" command="__XPBRh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL63B04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="__XO-6h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL63R04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F4" command="__XO_bx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL63h04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F6" command="__XPAkx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL63x04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X J" command="__XPAvR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL64B04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X M" command="__XO_xB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL64R04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X A" command="__XO-kB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL64h04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X B" command="__XPB3x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL64x04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X E" command="__XPAnh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL65B04EfCdtPSiNz5iWQ" keySequence="CTRL+F7" command="__XPAHx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL65R04EfCdtPSiNz5iWQ" keySequence="CTRL+F8" command="__XO_Lh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL65h04EfCdtPSiNz5iWQ" keySequence="CTRL+F9" command="__XO-9R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL65x04EfCdtPSiNz5iWQ" keySequence="CTRL+F11" command="__XPBWh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL66B04EfCdtPSiNz5iWQ" keySequence="CTRL+F12" command="__XO-4x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL66R04EfCdtPSiNz5iWQ" keySequence="CTRL+F4" command="__XO_8h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL66h04EfCdtPSiNz5iWQ" keySequence="CTRL+F6" command="__XO-1B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL66x04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+F7" command="__XPAyx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL67B04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+X G" command="__XPBgx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL67R04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="__XPAdR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL67h04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="__XPByx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL67x04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="__XPAXx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL68B04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+B D" command="__XPBlx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL68R04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+B F" command="__XO_Dx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL68h04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X O" command="__XPAex04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL68x04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X P" command="__XPBoR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL69B04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="__XPAiR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL69R04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X Q" command="__XO_CR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL69h04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X R" command="__XO_5x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL69x04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+X T" command="__XO_vh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6-B04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="__XO_TR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6-R04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+B R" command="__XPAZB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6-h04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+B S" command="__XPA9h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6-x04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+SHIFT+F12" command="__XPBnB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6_B04EfCdtPSiNz5iWQ" keySequence="DEL" command="__XO-2R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6_R04EfCdtPSiNz5iWQ" keySequence="ALT+-" command="__XPAMR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6_h04EfCdtPSiNz5iWQ" keySequence="ALT+CR" command="__XPA-R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL6_x04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D B" command="__XPBkR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7AB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D E" command="__XPB1B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7AR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D A" command="__XPBIR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ah04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D R" command="__XO_6R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ax04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D T" command="__XO-cB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7BB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D J" command="__XPA5B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7BR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D O" command="__XPAIx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Bh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D P" command="__XPBLx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Bx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+D Q" command="__XO_1h04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7CB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="__XL78x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7CR04EfCdtPSiNz5iWQ" keySequence="CTRL+CR" command="__XO_WB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ch04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+C" command="__XPABR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Cx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+R" command="__XO_rB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7DB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+U" command="__XPA0x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7DR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+I" command="__XO_ox04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Dh04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_UP" command="__XPApx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Dx04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_DOWN" command="__XO_XB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7EB04EfCdtPSiNz5iWQ" keySequence="SHIFT+INSERT" command="__XO-1h04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7ER04EfCdtPSiNz5iWQ" keySequence="INSERT" command="__XO_nB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Eh04EfCdtPSiNz5iWQ" keySequence="F4" command="__XO-qx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ex04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_UP" command="__XPA_R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7FB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="__XO_qB04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7FR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="__XL73h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7Fh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+P" command="__XPAyh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Fx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+T" command="__XO_cB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7GB04EfCdtPSiNz5iWQ" keySequence="CTRL+7" command="__XPANh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7GR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+M" command="__XO_Nx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Gh04EfCdtPSiNz5iWQ" keySequence="CTRL+/" command="__XPANh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Gx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+C" command="__XPANh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7HB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F" command="__XPBdR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7HR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+B" command="__XPB0x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Hh04EfCdtPSiNz5iWQ" keySequence="CTRL+T" command="__XPAPx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Hx04EfCdtPSiNz5iWQ" keySequence="CTRL+I" command="__XO_gB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7IB04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XO_wR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7IR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+/" command="__XO_5B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ih04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+R" command="__XO_7R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ix04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+U" command="__XPA6x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7JB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+'" command="__XPAUR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7JR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+O" command="__XO_Zh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Jh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+\" command="__XO-wR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Jx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+ARROW_UP" command="__XO_9R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7KB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="__XO_wh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7KR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_UP" command="__XO_1R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Kh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="__XO-5R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Kx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="__XO_iR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7LB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="__XO-vB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7LR04EfCdtPSiNz5iWQ" keySequence="CTRL+F3" command="__XPBrh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Lh04EfCdtPSiNz5iWQ" keySequence="CTRL+2 F" command="__XPBmR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Lx04EfCdtPSiNz5iWQ" keySequence="CTRL+2 R" command="__XPA-x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7MB04EfCdtPSiNz5iWQ" keySequence="CTRL+2 T" command="__XPANR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7MR04EfCdtPSiNz5iWQ" keySequence="CTRL+2 L" command="__XO-tR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Mh04EfCdtPSiNz5iWQ" keySequence="CTRL+2 M" command="__XO_jh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Mx04EfCdtPSiNz5iWQ" elementId="org.eclipse.core.runtime.xml" bindingContext="__XL7-x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7NB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+P" command="__XPBDh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7NR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+D" command="__XO_4h04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Nh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="__XL73B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7Nx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+P" command="__XO_mR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7OB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+G" command="__XPBWB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7OR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+H" command="__XPBFh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Oh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+R" command="__XO-kx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ox04EfCdtPSiNz5iWQ" keySequence="F3" command="__XPBRx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7PB04EfCdtPSiNz5iWQ" keySequence="F4" command="__XO-YB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7PR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_UP" command="__XPARB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ph04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="__XPASB04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Px04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="__XL74x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7QB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+P" command="__XPAQR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7QR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+A" command="__XPB4R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Qh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+C" command="__XPBcx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Qx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F" command="__XPBvh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7RB04EfCdtPSiNz5iWQ" keySequence="CTRL+I" command="__XPBOB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7RR04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XPAUB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Rh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+/" command="__XPAMB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Rx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+\" command="__XPAoB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7SB04EfCdtPSiNz5iWQ" keySequence="F3" command="__XPAPh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7SR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_UP" command="__XPAwB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Sh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="__XPA1R04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Sx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="__XO-pB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7TB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="__XPAfR04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7TR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="__XL73R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7Th04EfCdtPSiNz5iWQ" keySequence="CTRL+1" command="__XPBuh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Tx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="__XL77R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7UB04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+D" command="__XPAuB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7UR04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+P" command="__XPAzx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Uh04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+R" command="__XPBsR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Ux04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+S" command="__XO_HR04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7VB04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="__XL71x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7VR04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+V" command="__XO_lx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Vh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+C" command="__XPA8B04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Vx04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_UP" command="__XO-Yh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7WB04EfCdtPSiNz5iWQ" keySequence="ALT+ARROW_RIGHT" command="__XPBhh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7WR04EfCdtPSiNz5iWQ" keySequence="SHIFT+INSERT" command="__XO_lx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Wh04EfCdtPSiNz5iWQ" keySequence="CTRL+INSERT" command="__XPA8B04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="__XL75x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7XB04EfCdtPSiNz5iWQ" keySequence="CTRL+7" command="__XPANh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7XR04EfCdtPSiNz5iWQ" keySequence="CTRL+/" command="__XPANh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Xh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+C" command="__XPANh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="__XL73x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7YB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+M" command="__XO-fx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7YR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+C" command="__XPABR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Yh04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XPBpB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Yx04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+R" command="__XO_rB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7ZB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+S" command="__XO_dh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7ZR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+U" command="__XPA0x04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7Zh04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+I" command="__XO_ox04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7Zx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="__XL74h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7aB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+O" command="__XO-iB04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7aR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="__XL75h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7ah04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F" command="__XPBdR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7ax04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+R" command="__XO-mx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7bB04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+O" command="__XO-bB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7bR04EfCdtPSiNz5iWQ" keySequence="SHIFT+F2" command="__XPAUx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7bh04EfCdtPSiNz5iWQ" keySequence="F3" command="__XO-cx04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7bx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="__XL72x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7cB04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+F" command="__XO-uh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7cR04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XO-zh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7ch04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="__XL77B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7cx04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+M" command="__XO_ux04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7dB04EfCdtPSiNz5iWQ" keySequence="ALT+CTRL+N" command="__XPBnR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7dR04EfCdtPSiNz5iWQ" keySequence="CTRL+T" command="__XO_Oh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7dh04EfCdtPSiNz5iWQ" keySequence="CTRL+W" command="__XPAbh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7dx04EfCdtPSiNz5iWQ" keySequence="CTRL+N" command="__XPAmR04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7eB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="__XL7-h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7eR04EfCdtPSiNz5iWQ" keySequence="ALT+SHIFT+B" command="__XPB0x04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7eh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="__XL77h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7ex04EfCdtPSiNz5iWQ" keySequence="CTRL+R" command="__XPAIh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7fB04EfCdtPSiNz5iWQ" keySequence="F7" command="__XPBtB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7fR04EfCdtPSiNz5iWQ" keySequence="F8" command="__XPAXR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7fh04EfCdtPSiNz5iWQ" keySequence="F5" command="__XO-oR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7fx04EfCdtPSiNz5iWQ" keySequence="F6" command="__XO_rx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7gB04EfCdtPSiNz5iWQ" keySequence="CTRL+F2" command="__XPBEx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7gR04EfCdtPSiNz5iWQ" keySequence="CTRL+F5" command="__XPBgh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7gh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.console" bindingContext="__XL76B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7gx04EfCdtPSiNz5iWQ" keySequence="CTRL+R" command="__XPATB04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7hB04EfCdtPSiNz5iWQ" keySequence="CTRL+Z" command="__XPBpx04EfCdtPSiNz5iWQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="__XL7hR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="__XL77x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7hh04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+," command="__XPBSR04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7hx04EfCdtPSiNz5iWQ" keySequence="CTRL+SHIFT+." command="__XPBAx04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7iB04EfCdtPSiNz5iWQ" keySequence="CTRL+G" command="__XPBBB04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7iR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="__XL72h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7ih04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XO_1B04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7ix04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="__XL74R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7jB04EfCdtPSiNz5iWQ" keySequence="CTRL+O" command="__XO-iB04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7jR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="__XL79R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7jh04EfCdtPSiNz5iWQ" keySequence="CTRL+C" command="__XO_Px04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7jx04EfCdtPSiNz5iWQ" keySequence="CTRL+ARROW_LEFT" command="__XO-vR04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7kB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="__XL78R04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7kR04EfCdtPSiNz5iWQ" keySequence="CTRL+C" command="__XO-1R04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7kh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="__XL74B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7kx04EfCdtPSiNz5iWQ" keySequence="F1" command="__XO-cR04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7lB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="__XL79h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7lR04EfCdtPSiNz5iWQ" keySequence="F2" command="__XO-3B04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7lh04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="__XL79B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7lx04EfCdtPSiNz5iWQ" keySequence="F5" command="__XPBch04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7mB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="__XL76x04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7mR04EfCdtPSiNz5iWQ" keySequence="CTRL+INSERT" command="__XPAhB04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7mh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="__XL78h04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7mx04EfCdtPSiNz5iWQ" keySequence="ALT+Y" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7nB04EfCdtPSiNz5iWQ" keySequence="ALT+A" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7nR04EfCdtPSiNz5iWQ" keySequence="ALT+B" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7nh04EfCdtPSiNz5iWQ" keySequence="ALT+C" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7nx04EfCdtPSiNz5iWQ" keySequence="ALT+D" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7oB04EfCdtPSiNz5iWQ" keySequence="ALT+E" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7oR04EfCdtPSiNz5iWQ" keySequence="ALT+F" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7oh04EfCdtPSiNz5iWQ" keySequence="ALT+G" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7ox04EfCdtPSiNz5iWQ" keySequence="ALT+P" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7pB04EfCdtPSiNz5iWQ" keySequence="ALT+R" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7pR04EfCdtPSiNz5iWQ" keySequence="ALT+S" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7ph04EfCdtPSiNz5iWQ" keySequence="ALT+T" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7px04EfCdtPSiNz5iWQ" keySequence="ALT+V" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7qB04EfCdtPSiNz5iWQ" keySequence="ALT+W" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7qR04EfCdtPSiNz5iWQ" keySequence="ALT+H" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7qh04EfCdtPSiNz5iWQ" keySequence="ALT+L" command="__XPALh04EfCdtPSiNz5iWQ"/>
    <bindings xmi:id="__XL7qx04EfCdtPSiNz5iWQ" keySequence="ALT+N" command="__XPALh04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7rB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="__XL72B04EfCdtPSiNz5iWQ">
    <bindings xmi:id="__XL7rR04EfCdtPSiNz5iWQ" keySequence="ALT+CR" command="__XPAER04EfCdtPSiNz5iWQ"/>
  </bindingTables>
  <bindingTables xmi:id="__XL7rh04EfCdtPSiNz5iWQ" bindingContext="__XL7_B04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7rx04EfCdtPSiNz5iWQ" bindingContext="__XL7_R04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7sB04EfCdtPSiNz5iWQ" bindingContext="__XL7_h04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7sR04EfCdtPSiNz5iWQ" bindingContext="__XL7_x04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7sh04EfCdtPSiNz5iWQ" bindingContext="__XL8AB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7sx04EfCdtPSiNz5iWQ" bindingContext="__XL8AR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7tB04EfCdtPSiNz5iWQ" bindingContext="__XL8Ah04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7tR04EfCdtPSiNz5iWQ" bindingContext="__XL8Ax04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7th04EfCdtPSiNz5iWQ" bindingContext="__XL8BB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7tx04EfCdtPSiNz5iWQ" bindingContext="__XL8BR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7uB04EfCdtPSiNz5iWQ" bindingContext="__XL8Bh04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7uR04EfCdtPSiNz5iWQ" bindingContext="__XL8Bx04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7uh04EfCdtPSiNz5iWQ" bindingContext="__XL8CB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7ux04EfCdtPSiNz5iWQ" bindingContext="__XL8CR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7vB04EfCdtPSiNz5iWQ" bindingContext="__XL8Ch04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7vR04EfCdtPSiNz5iWQ" bindingContext="__XL8Cx04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7vh04EfCdtPSiNz5iWQ" bindingContext="__XL8DB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7vx04EfCdtPSiNz5iWQ" bindingContext="__XL8DR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7wB04EfCdtPSiNz5iWQ" bindingContext="__XL8Dh04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7wR04EfCdtPSiNz5iWQ" bindingContext="__XL8Dx04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7wh04EfCdtPSiNz5iWQ" bindingContext="__XL8EB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7wx04EfCdtPSiNz5iWQ" bindingContext="__XL8ER04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7xB04EfCdtPSiNz5iWQ" bindingContext="__XL8Eh04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7xR04EfCdtPSiNz5iWQ" bindingContext="__XL8Ex04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7xh04EfCdtPSiNz5iWQ" bindingContext="__XL8FB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7xx04EfCdtPSiNz5iWQ" bindingContext="__XL8FR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7yB04EfCdtPSiNz5iWQ" bindingContext="__XL8Fh04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7yR04EfCdtPSiNz5iWQ" bindingContext="__XL8Fx04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7yh04EfCdtPSiNz5iWQ" bindingContext="__XL8GB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7yx04EfCdtPSiNz5iWQ" bindingContext="__XL8GR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7zB04EfCdtPSiNz5iWQ" bindingContext="__XL8Gh04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7zR04EfCdtPSiNz5iWQ" bindingContext="__XL8Gx04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7zh04EfCdtPSiNz5iWQ" bindingContext="__XL8HB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL7zx04EfCdtPSiNz5iWQ" bindingContext="__XL8HR04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL70B04EfCdtPSiNz5iWQ" bindingContext="__XL8Hh04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL70R04EfCdtPSiNz5iWQ" bindingContext="__XL8Hx04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL70h04EfCdtPSiNz5iWQ" bindingContext="__XL8IB04EfCdtPSiNz5iWQ"/>
  <bindingTables xmi:id="__XL70x04EfCdtPSiNz5iWQ" bindingContext="__XL8IR04EfCdtPSiNz5iWQ"/>
  <rootContext xmi:id="__XL71B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="__XL71R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="__XL71h04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="__XL71x04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="__XL72B04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="__XL72R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="__XL72h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="__XL72x04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="__XL73B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="__XL73R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="__XL73h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="__XL73x04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="__XL74B04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="__XL74R04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="__XL74h04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="__XL74x04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="__XL75B04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="__XL75R04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="__XL75h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="__XL75x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="__XL76B04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="__XL76R04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="__XL76h04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="__XL76x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="__XL77B04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="__XL77R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="__XL77h04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="__XL77x04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="__XL78B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="__XL78R04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="__XL78h04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="__XL78x04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="__XL79B04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="__XL79R04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="__XL79h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="__XL79x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="__XL7-B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="__XL7-R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="__XL7-h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="__XL7-x04EfCdtPSiNz5iWQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="__XL7_B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="__XL7_R04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="__XL7_h04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="__XL7_x04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="__XL8AB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="__XL8AR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="__XL8Ah04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="__XL8Ax04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="__XL8BB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="__XL8BR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="__XL8Bh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="__XL8Bx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="__XL8CB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="__XL8CR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="__XL8Ch04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="__XL8Cx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="__XL8DB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="__XL8DR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="__XL8Dh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="__XL8Dx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="__XL8EB04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="__XL8ER04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="__XL8Eh04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="__XL8Ex04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="__XL8FB04EfCdtPSiNz5iWQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="__XL8FR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="__XL8Fh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="__XL8Fx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="__XL8GB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="__XL8GR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="__XL8Gh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="__XL8Gx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="__XL8HB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="__XL8HR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="__XL8Hh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="__XL8Hx04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="__XL8IB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="__XL8IR04EfCdtPSiNz5iWQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <descriptors xmi:id="__XL8Ih04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Ix04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="__XL8JB04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="__XL8JR04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Jh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Jx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8KB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8KR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Kh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Kx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8LB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8LR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Lh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Lx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="__XL8MB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="__XL8MR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Mh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Mx04EfCdtPSiNz5iWQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="__XL8NB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8NR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Nh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Nx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8OB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8OR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Oh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Ox04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="__XL8PB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="__XL8PR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Ph04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Px04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8QB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8QR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Qh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Qx04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="__XL8RB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="__XL8RR04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Rh04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Rx04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="__XL8SB04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="__XL8SR04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Sh04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Sx04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="__XL8TB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="__XL8TR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Th04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Tx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="__XL8UB04EfCdtPSiNz5iWQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="__XL8UR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Uh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Ux04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="__XL8VB04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="__XL8VR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Vh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Vx04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8WB04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="__XL8WR04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Wh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="__XL8XB04EfCdtPSiNz5iWQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="__XL8XR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Xh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="__XL8YB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8YR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Yh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Yx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8ZB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8ZR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Zh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8Zx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8aB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8aR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8ah04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="__XL8ax04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="__XL8bB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="__XL8bR04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="__XL8bh04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <trimContributions xmi:id="__XO-Sx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="__XO-TB04EfCdtPSiNz5iWQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="__XO-TR04EfCdtPSiNz5iWQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="__XO-Th04EfCdtPSiNz5iWQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="__XO-WB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-WR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Wh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-XB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="__XO-XR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Xh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-YB04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-YR04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Yh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="__XPCdx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Yx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ZB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ZR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Zh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-Zx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-aB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-aR04EfCdtPSiNz5iWQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-ah04EfCdtPSiNz5iWQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="__XO-ax04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-bB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-bR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-bh04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="__XPCcx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-bx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-cB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-cR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ch04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="__XPCfB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-cx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-dB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-dR04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="__XPCXR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-dh04EfCdtPSiNz5iWQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-dx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-eB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-eR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-eh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ex04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-fB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-fR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-fh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-fx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="__XPCYR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-gB04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-gR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-gh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-gx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-hB04EfCdtPSiNz5iWQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="__XO-hR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-hh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-hx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-iB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-iR04EfCdtPSiNz5iWQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="__XPCaB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ih04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ix04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-jB04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-jR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-jh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-jx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-kB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-kR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-kh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-kx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-lB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-lR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-lh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-lx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-mB04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-mR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-mh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-mx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-nB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-nR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-nh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="__XPCfR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-nx04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-oB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-oR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-oh04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ox04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-pB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-pR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ph04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-px04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-qB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-qR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-qh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="__XO-qx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-rB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-rR04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="__XPCbR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-rh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-rx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-sB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-sR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-sh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-sx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-tB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-tR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-th04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-tx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-uB04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-uR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-uh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="__XPCgR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-ux04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-vB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-vR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-vh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-vx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-wB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-wR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-wh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-xB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-xR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-xh04EfCdtPSiNz5iWQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="__XO-xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-yB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-yR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-yh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-yx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-zB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="__XO-zR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-zh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-zx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-0B04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-0R04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="__XPCWh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-0h04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-0x04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-1B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-1R04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-1h04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-1x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-2B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-2R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-2h04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-2x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-3B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-3R04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-3h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-3x04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-4B04EfCdtPSiNz5iWQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="__XO-4R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-4h04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-4x04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-5B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-5R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-5h04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="__XPCgB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-5x04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-6B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-6R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-6h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-6x04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-7B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-7R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-7h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="__XPCbB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO-7x04EfCdtPSiNz5iWQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="__XO-8B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-8R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-8h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-8x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-9B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-9R04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-9h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-9x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO--B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO--R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO--h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO--x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-_B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-_R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-_h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO-_x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="__XPCfB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_AB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_AR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ah04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ax04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_BB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_BR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Bh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Bx04EfCdtPSiNz5iWQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_CB04EfCdtPSiNz5iWQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="__XO_CR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ch04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Cx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_DB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_DR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Dh04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Dx04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_EB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ER04EfCdtPSiNz5iWQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Eh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ex04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_FB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_FR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Fh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Fx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_GB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_GR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Gh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Gx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="__XPCah04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_HB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_HR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="__XPCaR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Hh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="__XPCch04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_Hx04EfCdtPSiNz5iWQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="__XO_IB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_IR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ih04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ix04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_JB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_JR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Jh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Jx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_KB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_KR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Kh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Kx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_LB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_LR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Lh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Lx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_MB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_MR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Mh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="__XPCex04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_Mx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="__XO_NB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_NR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Nh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Nx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_OB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_OR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="__XPCYR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Oh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ox04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_PB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_PR04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ph04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Px04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_QB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_QR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="__XPCcB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_Qh04EfCdtPSiNz5iWQ" elementId="url" name="URL"/>
    <parameters xmi:id="__XO_Qx04EfCdtPSiNz5iWQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="__XO_RB04EfCdtPSiNz5iWQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="__XO_RR04EfCdtPSiNz5iWQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="__XO_Rh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Rx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_SB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_SR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Sh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Sx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="__XPCcB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_TB04EfCdtPSiNz5iWQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="__XO_TR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Th04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Tx04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_UB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_UR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Uh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Ux04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_VB04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="__XPCXB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_VR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Vh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Vx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_WB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_WR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Wh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_XB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_XR04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="__XPCXR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Xh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_YB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_YR04EfCdtPSiNz5iWQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="__XPCaB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Yh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Yx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ZB04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="__XPCXx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ZR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Zh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_Zx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_aB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_aR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ah04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ax04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_bB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_bR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_bh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_bx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_cB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_cR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ch04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_cx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_dB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_dR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_dh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="__XPCYR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_dx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_eB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_eR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_eh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ex04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="__XPCbB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_fB04EfCdtPSiNz5iWQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="__XO_fR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_fh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_fx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="__XPCfB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_gB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_gR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_gh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_gx04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_hB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_hR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_hh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_hx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_iB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_iR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ih04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ix04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_jB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_jR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_jh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_jx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_kB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_kR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_kh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_kx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_lB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_lR04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="__XPCcx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_lh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_lx04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="__XPCdx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_mB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_mR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_mh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_mx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="__XO_nB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_nR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_nh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_nx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_oB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_oR04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_oh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ox04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_pB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_pR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ph04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_px04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_qB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_qR04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="__XPCWh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_qh04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_qx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_rB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_rR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_rh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_rx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_sB04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_sR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="__XPCbB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_sh04EfCdtPSiNz5iWQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="__XO_sx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_tB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_tR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_th04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_tx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_uB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_uR04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_uh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_ux04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_vB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_vR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_vh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_vx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_wB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_wR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_wh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_wx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_xB04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_xR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_xh04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_xx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_yB04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_yR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_yh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_yx04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_zB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_zR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_zh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_zx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_0B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_0R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_0h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_0x04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="__XPCfx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_1B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_1R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_1h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_1x04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_2B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="__XPCcB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_2R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="__XO_2h04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_2x04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_3B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="__XPCch04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_3R04EfCdtPSiNz5iWQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="__XO_3h04EfCdtPSiNz5iWQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="__XO_3x04EfCdtPSiNz5iWQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="__XO_4B04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_4R04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_4h04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_4x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_5B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_5R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_5h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_5x04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_6B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_6R04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_6h04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_6x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_7B04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_7R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_7h04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_7x04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="__XPCbR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_8B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_8R04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_8h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_8x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_9B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_9R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_9h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_9x04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="__XO_-B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_-R04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO_-h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO_-x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="__XO__B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO__R04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XO__h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="__XPCeh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XO__x04EfCdtPSiNz5iWQ" elementId="title" name="Title"/>
    <parameters xmi:id="__XPAAB04EfCdtPSiNz5iWQ" elementId="message" name="Message"/>
    <parameters xmi:id="__XPAAR04EfCdtPSiNz5iWQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="__XPAAh04EfCdtPSiNz5iWQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="__XPAAx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPABB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPABR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPABh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPABx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPACB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPACR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPACh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPACx04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPADB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPADR04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPADh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPADx04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAEB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAER04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAEh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAEx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAFB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAFR04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="__XPCYh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAFh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAFx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAGB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAGR04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="__XPCbR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAGh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="__XPCcB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAGx04EfCdtPSiNz5iWQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="__XPAHB04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAHR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAHh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAHx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAIB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAIR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAIh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAIx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAJB04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAJR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAJh04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="__XPCcx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAJx04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAKB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAKR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAKh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAKx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPALB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPALR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPALh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="__XPCdx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPALx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAMB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAMR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAMh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAMx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPANB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPANR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPANh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPANx04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAOB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAOR04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAOh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAOx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAPB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAPR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAPh04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAPx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAQB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAQR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAQh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAQx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPARB04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPARR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPARh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPARx04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="__XPCXx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPASB04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPASR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPASh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPASx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPATB04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPATR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="__XPCex04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPATh04EfCdtPSiNz5iWQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="__XPATx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAUB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAUR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAUh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAUx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAVB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAVR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAVh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAVx04EfCdtPSiNz5iWQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="__XPCbx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAWB04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="__XPCXR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAWR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAWh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAWx04EfCdtPSiNz5iWQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAXB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAXR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAXh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAXx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAYB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAYR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAYh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAYx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAZB04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAZR04EfCdtPSiNz5iWQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAZh04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="__XPAZx04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="__XPAaB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAaR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAah04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAax04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAbB04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAbR04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="__XPAbh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAbx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAcB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAcR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAch04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAcx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAdB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAdR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAdh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAdx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAeB04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAeR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAeh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAex04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAfB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAfR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAfh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAfx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAgB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAgR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAgh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="__XPCfB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAgx04EfCdtPSiNz5iWQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAhB04EfCdtPSiNz5iWQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="__XPCaB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAhR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAhh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="__XPCXh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAhx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="__XPAiB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAiR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAih04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAix04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAjB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAjR04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="__XPCXx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAjh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAjx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAkB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAkR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAkh04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAkx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAlB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAlR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAlh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAlx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAmB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAmR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAmh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAmx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAnB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAnR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAnh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAnx04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAoB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAoR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="__XPCgB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAoh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAox04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPApB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPApR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAph04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPApx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAqB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAqR04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="__XPCXx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAqh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="__XPCWx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAqx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="__XPArB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="__XPArR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="__XPArh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPArx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAsB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAsR04EfCdtPSiNz5iWQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="__XPAsh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAsx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAtB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAtR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAth04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="__XPCXx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAtx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAuB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="__XPCaR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAuR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAuh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAux04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="__XPCZh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAvB04EfCdtPSiNz5iWQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="__XPAvR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAvh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAvx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="__XPCgR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAwB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAwR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="__XPCch04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAwh04EfCdtPSiNz5iWQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="__XPAwx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAxB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAxR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAxh04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAxx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPAyB04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="__XPAyR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAyh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAyx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAzB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAzR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAzh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPAzx04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="__XPCaR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA0B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA0R04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA0h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA0x04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA1B04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA1R04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA1h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA1x04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="__XPCbR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA2B04EfCdtPSiNz5iWQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="__XPCdR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA2R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA2h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA2x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA3B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA3R04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA3h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA3x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA4B04EfCdtPSiNz5iWQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA4R04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA4h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA4x04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA5B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA5R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA5h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA5x04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA6B04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA6R04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA6h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA6x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA7B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA7R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA7h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA7x04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA8B04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="__XPCdx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA8R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA8h04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA8x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="__XPCfR04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPA9B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="__XPA9R04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA9h04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA9x04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA-B04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA-R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA-h04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA-x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA_B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA_R04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA_h04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPA_x04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBAB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBAR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBAh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBAx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBBB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBBR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBBh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBBx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBCB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBCR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBCh04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBCx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBDB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBDR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBDh04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBDx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBEB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBER04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBEh04EfCdtPSiNz5iWQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="__XPCdR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBEx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBFB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBFR04EfCdtPSiNz5iWQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBFh04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBFx04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBGB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBGR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBGh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBGx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBHB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="__XPCfB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBHR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="__XPBHh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="__XPBHx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBIB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBIR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBIh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBIx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBJB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBJR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBJh04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBJx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBKB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBKR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBKh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBKx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBLB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBLR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBLh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBLx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBMB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBMR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBMh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="__XPCfR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBMx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBNB04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBNR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBNh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBNx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBOB04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBOR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBOh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBOx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBPB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBPR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBPh04EfCdtPSiNz5iWQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBPx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBQB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBQR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBQh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBQx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBRB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBRR04EfCdtPSiNz5iWQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBRh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBRx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBSB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBSR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBSh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBSx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBTB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBTR04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBTh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="__XPCeh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBTx04EfCdtPSiNz5iWQ" elementId="title" name="Title"/>
    <parameters xmi:id="__XPBUB04EfCdtPSiNz5iWQ" elementId="message" name="Message"/>
    <parameters xmi:id="__XPBUR04EfCdtPSiNz5iWQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="__XPBUh04EfCdtPSiNz5iWQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="__XPBUx04EfCdtPSiNz5iWQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="__XPBVB04EfCdtPSiNz5iWQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="__XPBVR04EfCdtPSiNz5iWQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="__XPBVh04EfCdtPSiNz5iWQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="__XPBVx04EfCdtPSiNz5iWQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="__XPBWB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBWR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBWh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBWx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBXB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBXR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBXh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBXx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBYB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBYR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBYh04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBYx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="__XPCfB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBZB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBZR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBZh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBZx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBaB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBaR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="__XPBah04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="__XPBax04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBbB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBbR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBbh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBbx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBcB04EfCdtPSiNz5iWQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="__XPBcR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBch04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="__XPCWx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBcx04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBdB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBdR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBdh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBdx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBeB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBeR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBeh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBex04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBfB04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="__XPCWh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBfR04EfCdtPSiNz5iWQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="__XPBfh04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="__XPBfx04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="__XPBgB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="__XPCdB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBgR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBgh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBgx04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBhB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBhR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBhh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="__XPCdx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBhx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBiB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBiR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBih04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBix04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBjB04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBjR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBjh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBjx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBkB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBkR04EfCdtPSiNz5iWQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBkh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBkx04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBlB04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBlR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBlh04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="__XPCbR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBlx04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBmB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBmR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBmh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBmx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBnB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBnR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBnh04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBnx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBoB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBoR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBoh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBox04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBpB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="__XPCZh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBpR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBph04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="__XPCYh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBpx04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBqB04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBqR04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBqh04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="__XPCax04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBqx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBrB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBrR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBrh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBrx04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBsB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBsR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="__XPCaR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBsh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBsx04EfCdtPSiNz5iWQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="__XPBtB04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBtR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="__XPCcB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBth04EfCdtPSiNz5iWQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="__XPBtx04EfCdtPSiNz5iWQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="__XPBuB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="__XPCch04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBuR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="__XPCbB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBuh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBux04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBvB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBvR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBvh04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBvx04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBwB04EfCdtPSiNz5iWQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBwR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBwh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="__XPCbB04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPBwx04EfCdtPSiNz5iWQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="__XPBxB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBxR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBxh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBxx04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPByB04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPByR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPByh04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPByx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="__XPCZR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBzB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBzR04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBzh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="__XPCeB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPBzx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB0B04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPB0R04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="__XPB0h04EfCdtPSiNz5iWQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="__XPB0x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="__XPCZx04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB1B04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB1R04EfCdtPSiNz5iWQ" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="__XPCch04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPB1h04EfCdtPSiNz5iWQ" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="__XPB1x04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="__XPCcR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB2B04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="__XPCfh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB2R04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="__XPCbh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB2h04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB2x04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="__XPCex04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB3B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB3R04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB3h04EfCdtPSiNz5iWQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB3x04EfCdtPSiNz5iWQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="__XPCeR04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB4B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="__XPCZB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB4R04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="__XPCXh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB4h04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="__XPCcB04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB4x04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB5B04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="__XPCZx04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPB5R04EfCdtPSiNz5iWQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="__XPB5h04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="__XPCgh04EfCdtPSiNz5iWQ">
    <parameters xmi:id="__XPB5x04EfCdtPSiNz5iWQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="__XPB6B04EfCdtPSiNz5iWQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="__XPB6R04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="__XPB6h04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB6x04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB7B04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB7R04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB7h04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB7x04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB8B04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB8R04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB8h04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB8x04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB9B04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB9R04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB9h04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB9x04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB-B04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB-R04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB-h04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB-x04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB_B04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB_R04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB_h04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPB_x04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCAB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCAR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCAh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCAx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCBB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCBR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCBh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCBx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCCB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCCR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCCh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCCx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCDB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCDR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCDh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCDx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCEB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCER04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCEh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCEx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCFB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCFR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCFh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCFx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCGB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCGR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCGh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCGx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCHB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCHR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCHh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCHx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCIB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCIR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCIh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCIx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCJB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCJR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCJh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCJx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCKB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCKR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCKh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCKx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCLB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCLR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCLh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCLx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCMB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCMR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCMh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCMx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCNB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCNR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCNh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCNx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCOB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCOR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCOh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCOx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCPB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCPR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCPh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCPx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCQB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCQR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCQh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCQx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCRB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCRR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCRh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCRx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCSB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCSR04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCSh04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCSx04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <commands xmi:id="__XPCTB04EfCdtPSiNz5iWQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="__XPCgh04EfCdtPSiNz5iWQ"/>
  <addons xmi:id="__XPCTR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="__XPCTh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="__XPCTx04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="__XPCUB04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="__XPCUR04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="__XPCUh04EfCdtPSiNz5iWQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="__XPCUx04EfCdtPSiNz5iWQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="__XPCVB04EfCdtPSiNz5iWQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="__XPCVR04EfCdtPSiNz5iWQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="__XPCVh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="__XPCVx04EfCdtPSiNz5iWQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="__XPCWB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="__XPCWR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="__XPCWh04EfCdtPSiNz5iWQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="__XPCWx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="__XPCXB04EfCdtPSiNz5iWQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="__XPCXR04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="__XPCXh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="__XPCXx04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="__XPCYB04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="__XPCYR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="__XPCYh04EfCdtPSiNz5iWQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="__XPCYx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="__XPCZB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="__XPCZR04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="__XPCZh04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="__XPCZx04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="__XPCaB04EfCdtPSiNz5iWQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="__XPCaR04EfCdtPSiNz5iWQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="__XPCah04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="__XPCax04EfCdtPSiNz5iWQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="__XPCbB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="__XPCbR04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="__XPCbh04EfCdtPSiNz5iWQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="__XPCbx04EfCdtPSiNz5iWQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="__XPCcB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="__XPCcR04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="__XPCch04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="__XPCcx04EfCdtPSiNz5iWQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="__XPCdB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="__XPCdR04EfCdtPSiNz5iWQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="__XPCdh04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="__XPCdx04EfCdtPSiNz5iWQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="__XPCeB04EfCdtPSiNz5iWQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="__XPCeR04EfCdtPSiNz5iWQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="__XPCeh04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="__XPCex04EfCdtPSiNz5iWQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="__XPCfB04EfCdtPSiNz5iWQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="__XPCfR04EfCdtPSiNz5iWQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="__XPCfh04EfCdtPSiNz5iWQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="__XPCfx04EfCdtPSiNz5iWQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="__XPCgB04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="__XPCgR04EfCdtPSiNz5iWQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="__XPCgh04EfCdtPSiNz5iWQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
</application:Application>
