package com.paximum.demo.models;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetOffersResponse {

    private Header header;
    private Body body;

    public GetOffersResponse() {
    }

    public Header getHeader() {
        return header;
    }
    public void setHeader(Header header) {
        this.header = header;
    }
    public Body getBody() {
        return body;
    }
    public void setBody(Body body) {
        this.body = body;
    }

    // Classe Header
    public static class Header {
        private String requestId;
        private boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {
        }

        public String getRequestId() {
            return requestId;
        }
        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }
        public boolean isSuccess() {
            return success;
        }
        public void setSuccess(boolean success) {
            this.success = success;
        }
        public String getResponseTime() {
            return responseTime;
        }
        public void setResponseTime(String responseTime) {
            this.responseTime = responseTime;
        }
        public List<Message> getMessages() {
            return messages;
        }
        public void setMessages(List<Message> messages) {
            this.messages = messages;
        }
    }

    // Classe Message
    public static class Message {
        private int id;
        private String code;
        private int messageType;
        private String message;

        public Message() {
        }

        public int getId() {
            return id;
        }
        public void setId(int id) {
            this.id = id;
        }
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
        public int getMessageType() {
            return messageType;
        }
        public void setMessageType(int messageType) {
            this.messageType = messageType;
        }
        public String getMessage() {
            return message;
        }
        public void setMessage(String message) {
            this.message = message;
        }
    }

    // Classe Body
    public static class Body {
        private List<Offer> offers;

        public Body() {
        }

        public List<Offer> getOffers() {
            return offers;
        }
        public void setOffers(List<Offer> offers) {
            this.offers = offers;
        }
    }

    // Classe Offer (chaque offre dans le tableau "offers")
    public static class Offer {
        private String flightId;
        private int segmentNumber;
        private List<FlightClassInformation> flightClassInformations;
        private List<BaggageInformation> baggageInformations;
        private List<String> groupKeys;
        private List<OfferId> offerIds;
        private boolean isPackageOffer;
        private int route;
        private FlightBrandInfo flightBrandInfo;
        private String offerId;
        private Price price;
        private int provider;

        public Offer() {
        }

        public String getFlightId() {
            return flightId;
        }
        public void setFlightId(String flightId) {
            this.flightId = flightId;
        }
        public int getSegmentNumber() {
            return segmentNumber;
        }
        public void setSegmentNumber(int segmentNumber) {
            this.segmentNumber = segmentNumber;
        }
        public List<FlightClassInformation> getFlightClassInformations() {
            return flightClassInformations;
        }
        public void setFlightClassInformations(List<FlightClassInformation> flightClassInformations) {
            this.flightClassInformations = flightClassInformations;
        }
        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }
        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }
        public List<String> getGroupKeys() {
            return groupKeys;
        }
        public void setGroupKeys(List<String> groupKeys) {
            this.groupKeys = groupKeys;
        }
        public List<OfferId> getOfferIds() {
            return offerIds;
        }
        public void setOfferIds(List<OfferId> offerIds) {
            this.offerIds = offerIds;
        }
        public boolean isPackageOffer() {
            return isPackageOffer;
        }
        public void setPackageOffer(boolean isPackageOffer) {
            this.isPackageOffer = isPackageOffer;
        }
        public int getRoute() {
            return route;
        }
        public void setRoute(int route) {
            this.route = route;
        }
        public FlightBrandInfo getFlightBrandInfo() {
            return flightBrandInfo;
        }
        public void setFlightBrandInfo(FlightBrandInfo flightBrandInfo) {
            this.flightBrandInfo = flightBrandInfo;
        }
        public String getOfferId() {
            return offerId;
        }
        public void setOfferId(String offerId) {
            this.offerId = offerId;
        }
        public Price getPrice() {
            return price;
        }
        public void setPrice(Price price) {
            this.price = price;
        }
        public int getProvider() {
            return provider;
        }
        public void setProvider(int provider) {
            this.provider = provider;
        }
    }

    // Classe FlightClassInformation
    public static class FlightClassInformation {
        private int type;
        private String segmentId;
        private String name;
        private String id;
        private String code;

        public FlightClassInformation() {
        }

        public int getType() {
            return type;
        }
        public void setType(int type) {
            this.type = type;
        }
        public String getSegmentId() {
            return segmentId;
        }
        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
    }

    // Classe BaggageInformation
    public static class BaggageInformation {
        private String segmentId;
        private int weight;
        private int piece;
        private int baggageType;
        private int unitType;
        private int passengerType;

        public BaggageInformation() {
        }

        public String getSegmentId() {
            return segmentId;
        }
        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }
        public int getWeight() {
            return weight;
        }
        public void setWeight(int weight) {
            this.weight = weight;
        }
        public int getPiece() {
            return piece;
        }
        public void setPiece(int piece) {
            this.piece = piece;
        }
        public int getBaggageType() {
            return baggageType;
        }
        public void setBaggageType(int baggageType) {
            this.baggageType = baggageType;
        }
        public int getUnitType() {
            return unitType;
        }
        public void setUnitType(int unitType) {
            this.unitType = unitType;
        }
        public int getPassengerType() {
            return passengerType;
        }
        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }
    }

    // Classe OfferId
    public static class OfferId {
        private String groupKey;
        private String offerId;

        public OfferId() {
        }

        public String getGroupKey() {
            return groupKey;
        }
        public void setGroupKey(String groupKey) {
            this.groupKey = groupKey;
        }
        public String getOfferId() {
            return offerId;
        }
        public void setOfferId(String offerId) {
            this.offerId = offerId;
        }
    }

    // Classe FlightBrandInfo
    public static class FlightBrandInfo {
        private List<Feature> features;
        private String id;
        private String name;

        public FlightBrandInfo() {
        }

        public List<Feature> getFeatures() {
            return features;
        }
        public void setFeatures(List<Feature> features) {
            this.features = features;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
    }

    // Classe Feature (dans FlightBrandInfo)
    public static class Feature {
        private String commercialName;
        private int serviceGroup;
        private int pricingType;
        private List<Explanation> explanations;

        public Feature() {
        }

        public String getCommercialName() {
            return commercialName;
        }
        public void setCommercialName(String commercialName) {
            this.commercialName = commercialName;
        }
        public int getServiceGroup() {
            return serviceGroup;
        }
        public void setServiceGroup(int serviceGroup) {
            this.serviceGroup = serviceGroup;
        }
        public int getPricingType() {
            return pricingType;
        }
        public void setPricingType(int pricingType) {
            this.pricingType = pricingType;
        }
        public List<Explanation> getExplanations() {
            return explanations;
        }
        public void setExplanations(List<Explanation> explanations) {
            this.explanations = explanations;
        }
    }

    // Classe Explanation
    public static class Explanation {
        private String text;

        public Explanation() {
        }

        public String getText() {
            return text;
        }
        public void setText(String text) {
            this.text = text;
        }
    }

    // Classe Price
    public static class Price {
        private double amount;
        private String currency;

        public Price() {
        }

        public double getAmount() {
            return amount;
        }
        public void setAmount(double amount) {
            this.amount = amount;
        }
        public String getCurrency() {
            return currency;
        }
        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
