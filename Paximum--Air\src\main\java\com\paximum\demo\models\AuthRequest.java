package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AuthRequest {

    @JsonProperty("Agency")
    private String agency;

    @JsonProperty("User")
    private String user;

    @JsonProperty("Password")
    private String password;

    // Constructeur par défaut nécessaire pour Jackson
    public AuthRequest() {}

    public AuthRequest(String agency, String user, String password) {
        this.agency = agency;
        this.user = user;
        this.password = password;
    }

    public String getAgency() {
        return agency;
    }

    public void setAgency(String agency) {
        this.agency = agency;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
