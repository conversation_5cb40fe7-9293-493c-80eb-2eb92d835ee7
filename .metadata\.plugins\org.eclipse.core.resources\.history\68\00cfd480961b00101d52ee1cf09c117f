package com.paximum.demo.controllers;

import org.springframework.web.bind.annotation.*;
import com.paximum.demo.services.BookingService;
import com.paximum.demo.models.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/booking")
@CrossOrigin
public class BookingController {
    
    private final BookingService bookingService;
    
    public BookingController(BookingService bookingService) {
        this.bookingService = bookingService;
    }
    

 
    @PostMapping("/begin-transaction")
    public Mono<BeginTransactionResponse> beginTransaction(
            @RequestBody BeginTransactionRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.beginTransaction(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Erreur lors du début de la transaction", e));
    }
    
    
    @PostMapping("/set-reservation-info")
    public Mono<SetReservationInfoResponse> setReservationInfo(
            @RequestBody SetReservationInfoRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.setReservationInfo(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Erreur lors de la définition des informations de réservation", e));
    }
    
    @PostMapping("/commit-transaction")
    public Mono<CommitTransactionResponse> commitTransaction(
            @RequestBody CommitTransactionRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.commitTransaction(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error finalizing transaction: " + e.getMessage(), e));
    }
    
    @PostMapping("/add-services")
    public Mono<AddServicesResponse> addServices(
            @RequestBody AddServicesRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.addServices(request, token.replace("Bearer ", ""))
            .onErrorResume(e -> {
                if (e.getMessage() != null && e.getMessage().contains("EncodedOfferIdModel.getSearchId()")) {
                    return Mono.error(new RuntimeException("Invalid offer ID format. The searchId is missing in the encoded offer.", e));
                }
                return Mono.error(new RuntimeException("Failed to add services: " + e.getMessage(), e));
            });
    }
    
    @PostMapping("/remove-services")
    public Mono<RemoveServicesResponse> removeServices(
            @RequestBody RemoveServicesRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.removeServices(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error removing services", e));
    }
    
  
    
    @PostMapping("/get-reservation-detail")
    public Mono<GetReservationDetailResponse> getReservationDetail(
            @RequestBody GetReservationDetailRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getReservationDetail(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error calling get-reservation-detail", e));
    }
    
    @PostMapping("/get-reservation-list")
    public Mono<GetReservationListResponse> getReservationList(
            @RequestBody GetReservationListRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getReservationList(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error fetching reservation list", e));
    }
    
    @PostMapping("/get-payment-list")
    public Mono<GetPaymentListResponse> getPaymentList(
            @RequestBody GetPaymentListRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getPaymentList(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error retrieving payment list", e));
    }
    
    @PostMapping("/get-cancellation-penalty")
    public Mono<GetCancellationPenaltyResponse> getCancellationPenalty(
            @RequestBody GetCancellationPenaltyRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.getCancellationPenalty(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error getting cancellation penalty", e));
    }
    
    @PostMapping("/cancel-reservation")
    public Mono<CancelReservationResponse> cancelReservation(
            @RequestBody CancelReservationRequest request,
            @RequestHeader("Authorization") String token) {
        return bookingService.cancelReservation(request, token.replace("Bearer ", ""))
            .onErrorMap(e -> new RuntimeException("Error cancelling reservation", e));
    }
}