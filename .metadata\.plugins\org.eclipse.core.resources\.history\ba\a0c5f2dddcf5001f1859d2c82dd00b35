package com.example.demo.service;

import com.example.demo.dto.AuthRequest;
import com.example.demo.dto.AuthResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
public class AuthService {

    private final WebClient webClient;

    public AuthService() {
        this.webClient = WebClient.builder()
                .baseUrl("http://service.stage.paximum.com/v2") // 🔹 URL de l'API TourVisio
                .build();
    }

    public Mono<String> authenticate(String agency, String user, String password) {
        AuthRequest authRequest = new AuthRequest(agency, user, password);

        return webClient.post()
                .uri("/api/authenticationservice/login")
                .bodyValue(authRequest)
                .retrieve()
                .bodyToMono(AuthResponse.class)
                .map(authResponse -> authResponse.getBody().getToken())
                .onErrorResume(error -> {
                    return Mono.error(new RuntimeException("Erreur d'authentification: " + error.getMessage()));
                });
    }
}
