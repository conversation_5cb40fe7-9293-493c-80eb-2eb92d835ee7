<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_4pvREBuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_4pvRERuIEfCLRNFn66vwGw" bindingContexts="_4pvRGhuIEfCLRNFn66vwGw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_4pvRERuIEfCLRNFn66vwGw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_4_feohuIEfCLRNFn66vwGw" label="%trimmedwindow.label.eclipseSDK" x="32" y="32" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1744893524930"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_4_feohuIEfCLRNFn66vwGw" selectedElement="_4_gFsBuIEfCLRNFn66vwGw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_4_gFsBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_5OlqkBuIEfCLRNFn66vwGw">
        <children xsi:type="advanced:Perspective" xmi:id="_5OlqkBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_5OlqkRuIEfCLRNFn66vwGw" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$Ctrl+Shift+T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_5OlqkRuIEfCLRNFn66vwGw" selectedElement="_5OlqkhuIEfCLRNFn66vwGw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_5OlqkhuIEfCLRNFn66vwGw" containerData="2500" selectedElement="_5OlqkxuIEfCLRNFn66vwGw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_5OlqkxuIEfCLRNFn66vwGw" containerData="6000" selectedElement="_5OlqlBuIEfCLRNFn66vwGw">
                <children xsi:type="basic:PartStack" xmi:id="_5OlqlBuIEfCLRNFn66vwGw" elementId="left" containerData="6600" selectedElement="_5OlqlxuIEfCLRNFn66vwGw">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <tags>active</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5OlqlRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_5OTWsBuIEfCLRNFn66vwGw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5OlqlhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_5OWaABuIEfCLRNFn66vwGw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5OlqlxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_5OWaARuIEfCLRNFn66vwGw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5OlqmBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_5OjOUBuIEfCLRNFn66vwGw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_5OlqmRuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" toBeRendered="false" containerData="3400">
                  <children xsi:type="advanced:Placeholder" xmi:id="_5OlqmhuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_5OjOUxuIEfCLRNFn66vwGw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_5OlqmxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqnBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_5OjOURuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_5OlqnRuIEfCLRNFn66vwGw" containerData="7500" selectedElement="_5OlqnhuIEfCLRNFn66vwGw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_5OlqnhuIEfCLRNFn66vwGw" containerData="7500" selectedElement="_5OlqoBuIEfCLRNFn66vwGw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqnxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_5OFUQBuIEfCLRNFn66vwGw"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_5OlqoBuIEfCLRNFn66vwGw" containerData="2500" selectedElement="_5OlqoRuIEfCLRNFn66vwGw">
                  <children xsi:type="basic:PartStack" xmi:id="_5OlqoRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_5OlqohuIEfCLRNFn66vwGw">
                    <children xsi:type="advanced:Placeholder" xmi:id="_5OlqohuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_5OinQBuIEfCLRNFn66vwGw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_5OlqoxuIEfCLRNFn66vwGw" elementId="right" containerData="5000" selectedElement="_5OlqpBuIEfCLRNFn66vwGw">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5OlqpBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ContentOutline" ref="_5OZdURuIEfCLRNFn66vwGw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5OlqpRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_5OaEYBuIEfCLRNFn66vwGw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5OlqphuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_5OduwBuIEfCLRNFn66vwGw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5OlqpxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_5OjOUhuIEfCLRNFn66vwGw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5OlqqBuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_5OlDgBuIEfCLRNFn66vwGw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_5OlqqRuIEfCLRNFn66vwGw" elementId="bottom" containerData="2500" selectedElement="_5OlqqhuIEfCLRNFn66vwGw">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqqhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProblemView" ref="_5OWaAhuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqqxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavadocView" ref="_5OXBEBuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqrBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.SourceView" ref="_5OXoIBuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqrRuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_5OYPMBuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqrhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_5OY2QBuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqrxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_5OY2QRuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqsBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_5OZdUBuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5OlqsRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_5OkccBuIEfCLRNFn66vwGw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_4_gFsRuIEfCLRNFn66vwGw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_4_gFshuIEfCLRNFn66vwGw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_4_e3kBuIEfCLRNFn66vwGw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_4_gFsxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_4_feoBuIEfCLRNFn66vwGw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_4_gFtBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_4_feoRuIEfCLRNFn66vwGw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_4_e3kBuIEfCLRNFn66vwGw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_4_feoBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_4_feoRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_5OFUQBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_5OFUQRuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OTWsBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xD;&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xD;&#xA;&lt;xmlDefinedFilters>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/xmlDefinedFilters>&#xD;&#xA;&lt;/customFilters>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_5XRFcBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5XRsgBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OWaABuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OWaARuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_JTOy4BuJEfC5QsvOhuhJcg" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JTOy4RuJEfC5QsvOhuhJcg" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OWaAhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;120&quot; org.eclipse.ui.ide.markerType=&quot;120&quot; org.eclipse.ui.ide.pathField=&quot;160&quot; org.eclipse.ui.ide.resourceField=&quot;120&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;400&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5yjTQBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5yjTQRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OXBEBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OXoIBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OYPMBuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OY2QBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OY2QRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OZdUBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OZdURuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5vMdQBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5vMdQRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OaEYBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OduwBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OinQBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xD;&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xD;&#xA;&lt;sorter>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;filteredTreeFindHistory/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_5of_YBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5of_YRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OjOUBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OjOURuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OjOUhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OjOUxuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_5jzsoBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jzsoRuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OkccBuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5OlDgBuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <trimBars xmi:id="_4pvREhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_5Eky8BuIEfCLRNFn66vwGw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5Eky8RuIEfCLRNFn66vwGw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5EnPMBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ksqAEBxjEfCmkM_GFMmtwA" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_4rBDcRuIEfCLRNFn66vwGw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5EnPMRuIEfCLRNFn66vwGw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5EnPMhuIEfCLRNFn66vwGw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5En2QBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ksrOMBxjEfCmkM_GFMmtwA" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_4q8K_xuIEfCLRNFn66vwGw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ksrOMRxjEfCmkM_GFMmtwA" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_4q-ARhuIEfCLRNFn66vwGw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5En2QRuIEfCLRNFn66vwGw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5En2QhuIEfCLRNFn66vwGw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5RmiUBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5QcrwBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5RM5sBuIEfCLRNFn66vwGw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5En2QxuIEfCLRNFn66vwGw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5En2RBuIEfCLRNFn66vwGw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5En2RRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ksscUBxjEfCmkM_GFMmtwA" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_4q_1bhuIEfCLRNFn66vwGw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5En2RhuIEfCLRNFn66vwGw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5En2RxuIEfCLRNFn66vwGw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5En2SBuIEfCLRNFn66vwGw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5En2SRuIEfCLRNFn66vwGw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5EodUBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5Fv3oBuIEfCLRNFn66vwGw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5FwesBuIEfCLRNFn66vwGw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_4pvRExuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_4pvRFBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_4pvRFRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_4pvRFhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_4pvRFxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" side="Left"/>
    <trimBars xmi:id="_4pvRGBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_4pvRGRuIEfCLRNFn66vwGw" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_4pvRGhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rxRZxuIEfCLRNFn66vwGw" keySequence="CTRL+1" command="_4q683BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4ryfgRuIEfCLRNFn66vwGw" keySequence="CTRL+6" command="_4q7j9BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rzGkhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+I" command="_4q6V0BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r0UsxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+L" command="_4rBDqRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07xxuIEfCLRNFn66vwGw" keySequence="CTRL+SPACE" command="_4q_1jRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i3BuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+D" command="_4rBqoxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_FxuIEfCLRNFn66vwGw" keySequence="CTRL+V" command="_4q2rbRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NQRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+SPACE" command="_4q7j6BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NQhuIEfCLRNFn66vwGw" keySequence="CTRL+A" command="_4q9ZMxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50RhuIEfCLRNFn66vwGw" keySequence="CTRL+C" command="_4q-nUBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CZhuIEfCLRNFn66vwGw" keySequence="CTRL+X" command="_4q8LBRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7pcBuIEfCLRNFn66vwGw" keySequence="CTRL+Y" command="_4q-ARhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7pchuIEfCLRNFn66vwGw" keySequence="CTRL+Z" command="_4q8K_xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eoRuIEfCLRNFn66vwGw" keySequence="ALT+PAGE_UP" command="_4q-AVhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eohuIEfCLRNFn66vwGw" keySequence="ALT+PAGE_DOWN" command="_4q_OjBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9ephuIEfCLRNFn66vwGw" keySequence="SHIFT+INSERT" command="_4q2rbRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eqxuIEfCLRNFn66vwGw" keySequence="ALT+F11" command="_4q5HoRuIEfCLRNFn66vwGw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_4sAh9BuIEfCLRNFn66vwGw" keySequence="CTRL+F10" command="_4q35hxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJAhuIEfCLRNFn66vwGw" keySequence="CTRL+INSERT" command="_4q-nUBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwEhuIEfCLRNFn66vwGw" keySequence="CTRL+PAGE_UP" command="_4rBDihuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwExuIEfCLRNFn66vwGw" keySequence="CTRL+PAGE_DOWN" command="_4q685RuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwFRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+F1" command="_4q5utxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwFhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+F2" command="_4q_OdBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwFxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+F3" command="_4rBDfhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwHBuIEfCLRNFn66vwGw" keySequence="SHIFT+DEL" command="_4q8LBRuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rpVkBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_4rHxIRuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rs_8BuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+CR" command="_4rBDfRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rwDQBuIEfCLRNFn66vwGw" keySequence="CTRL+BS" command="_4q2EVhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rxRZBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+Q" command="_4q6V1huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rztohuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+J" command="_4q6VyxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rztphuIEfCLRNFn66vwGw" keySequence="CTRL++" command="_4q_OaRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r0UuRuIEfCLRNFn66vwGw" keySequence="CTRL+-" command="_4q9ZFRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07wxuIEfCLRNFn66vwGw" keySequence="CTRL+/" command="_4q2EYRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i2BuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+C" command="_4q2EaBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i2huIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+C" command="_4q2EYRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J4RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F" command="_4q7j8xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2w8huIEfCLRNFn66vwGw" keySequence="ALT+CTRL+J" command="_4q680xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3YAxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+A" command="_4q-nchuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_FBuIEfCLRNFn66vwGw" keySequence="CTRL+T" command="_4rBquRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mJBuIEfCLRNFn66vwGw" keySequence="CTRL+J" command="_4q35jRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mKBuIEfCLRNFn66vwGw" keySequence="CTRL+L" command="_4rAcahuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NNxuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4q-nVhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NPhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+/" command="_4q8K8RuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50TBuIEfCLRNFn66vwGw" keySequence="CTRL+D" command="_4q4gmBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CYBuIEfCLRNFn66vwGw" keySequence="CTRL+=" command="_4q_OaRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CZBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Y" command="_4q02MhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7pdRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+DEL" command="_4q_1khuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7pdhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+X" command="_4q-nWhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r8QgBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+Y" command="_4q9ZEhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83khuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+\" command="_4q-nihuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83lBuIEfCLRNFn66vwGw" keySequence="CTRL+DEL" command="_4q8K9xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83lxuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_UP" command="_4rCRshuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83mRuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_DOWN" command="_4q_OmBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eoxuIEfCLRNFn66vwGw" keySequence="SHIFT+END" command="_4q9ZHhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9erBuIEfCLRNFn66vwGw" keySequence="SHIFT+HOME" command="_4q8yFhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FtBuIEfCLRNFn66vwGw" keySequence="END" command="_4rBDlxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FthuIEfCLRNFn66vwGw" keySequence="INSERT" command="_4q-nlRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FvBuIEfCLRNFn66vwGw" keySequence="F2" command="_4q685xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FyRuIEfCLRNFn66vwGw" keySequence="HOME" command="_4rBqhhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FzBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+ARROW_UP" command="_4rBquxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-swBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+ARROW_DOWN" command="_4q-AJxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-swxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+INSERT" command="_4q5uyBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-syBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_4q9ZIRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T0RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_4q6VwxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh9RuIEfCLRNFn66vwGw" keySequence="CTRL+F10" command="_4rBDdxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJABuIEfCLRNFn66vwGw" keySequence="CTRL+END" command="_4q_OnBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJDBuIEfCLRNFn66vwGw" keySequence="CTRL+ARROW_UP" command="_4q5utRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJDRuIEfCLRNFn66vwGw" keySequence="CTRL+ARROW_DOWN" command="_4rCRwxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwEBuIEfCLRNFn66vwGw" keySequence="CTRL+ARROW_LEFT" command="_4q-nShuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwERuIEfCLRNFn66vwGw" keySequence="CTRL+ARROW_RIGHT" command="_4q6V1RuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwFBuIEfCLRNFn66vwGw" keySequence="CTRL+HOME" command="_4q2rbBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwGBuIEfCLRNFn66vwGw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_4q_1WxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwGRuIEfCLRNFn66vwGw" keySequence="CTRL+NUMPAD_ADD" command="_4rBqqRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwGhuIEfCLRNFn66vwGw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_4rBDexuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwGxuIEfCLRNFn66vwGw" keySequence="CTRL+NUMPAD_DIVIDE" command="_4q5uuRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwJxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_4q_1YRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-MRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_4q-nmxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-TBuIEfCLRNFn66vwGw" keySequence="ALT+/" command="_4rBqiRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-TRuIEfCLRNFn66vwGw" keySequence="SHIFT+CR" command="_4rBqhRuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4ru1IBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.contexts.window" bindingContext="_4pvRGxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rvcMBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+A" command="_4q_OmhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rwDQxuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+T" command="_4q35hhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rwDRBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+L" command="_4q-niRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rwDRRuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+M" command="_4rBqmRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rxRYBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q O" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4rxRYRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_4rxRYhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q P" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4rxRYxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_4rxRaBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+B" command="_4q_OhRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4cBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+R" command="_4rCRxxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4cRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q Q" command="_4q_OfRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4chuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+S" command="_4q_OXRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4cxuIEfCLRNFn66vwGw" keySequence="CTRL+3" command="_4q685huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4dBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+T" command="_4q8LBBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4dhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+T" command="_4q5uvBuIEfCLRNFn66vwGw">
      <tags>deleted</tags>
    </bindings>
    <bindings xmi:id="_4rx4dxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q S" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4rx4eBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_4rx4exuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+U" command="_4q5uxxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4fBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q T" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4rx4fRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_4rx4fhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+V" command="_4rBDmhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4ryfghuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q V" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4ryfgxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_4ryfhxuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+G" command="_4q_OchuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rzGkBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+W" command="_4q8LAxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rzGkRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+H" command="_4q-nRBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rztoBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q H" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4rztoRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_4rztoxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q J" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4rztpBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_4rztpRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+K" command="_4q5ushuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r0UsBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q K" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r0UsRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_4r0UshuIEfCLRNFn66vwGw" keySequence="CTRL+," command="_4q3ScBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r0UtxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q L" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r0UuBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_4r0UuhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+N" command="_4q-nZRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07wBuIEfCLRNFn66vwGw" keySequence="CTRL+." command="_4rCRkxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07xBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+O" command="_4rBqnRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07yBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+P" command="_4q5HpRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i0BuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+A" command="_4q_OihuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i0huIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+B" command="_4q5usxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i1BuIEfCLRNFn66vwGw" keySequence="CTRL+#" command="_4q35iBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i3huIEfCLRNFn66vwGw" keySequence="ALT+CTRL+T" command="_4q-nSxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i3xuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+E" command="_4q5uwhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J5RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+G" command="_4rCRoBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J5xuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+H" command="_4q3ShRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J6RuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q X" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r2J6huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_4r2w8BuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q Y" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r2w8RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_4r2w8xuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q Z" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r2w9BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_4r3YARuIEfCLRNFn66vwGw" keySequence="CTRL+P" command="_4rBDcRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3YAhuIEfCLRNFn66vwGw" keySequence="CTRL+Q" command="_4rBDhBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3YCxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+C" command="_4q_1kRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_ERuIEfCLRNFn66vwGw" keySequence="CTRL+S" command="_4q9ZFxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_FRuIEfCLRNFn66vwGw" keySequence="CTRL+U" command="_4q-AQRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_FhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+F" command="_4rBDiBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mIBuIEfCLRNFn66vwGw" keySequence="CTRL+W" command="_4q-AUBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mIRuIEfCLRNFn66vwGw" keySequence="CTRL+H" command="_4q_1jBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mJRuIEfCLRNFn66vwGw" keySequence="CTRL+K" command="_4q_OhxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NMBuIEfCLRNFn66vwGw" keySequence="CTRL+M" command="_4q_1iBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NMxuIEfCLRNFn66vwGw" keySequence="CTRL+N" command="_4rCRpRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NQBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+P" command="_4q_OdhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NRBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+R" command="_4q-ASxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NSRuIEfCLRNFn66vwGw" keySequence="CTRL+B" command="_4q3ScxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50QBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q B" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r50QRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_4r50RxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+S" command="_4q-nORuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50SRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+T" command="_4q-ndRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50ShuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q C" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r50SxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_4r50TRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Q D" command="_4q_OfRuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r50ThuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_4r50UhuIEfCLRNFn66vwGw" keySequence="CTRL+E" command="_4q8K9BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50UxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+V" command="_4q9ZIBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50VBuIEfCLRNFn66vwGw" keySequence="CTRL+F" command="_4q4goBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bUhuIEfCLRNFn66vwGw" keySequence="CTRL+G" command="_4q2EVxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bUxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+W" command="_4rBquhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bVBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+H" command="_4q7j-huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bVRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+I" command="_4q35iRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bWBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+J" command="_4q8K8huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bWRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+K" command="_4q9ZGhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bWhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+L" command="_4q682BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bWxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+M" command="_4rBqqhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CYRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+N" command="_4q8LABuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CZRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+Z" command="_4q-nPhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7pcxuIEfCLRNFn66vwGw" keySequence="CTRL+_" command="_4q7j6xuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r7pdBuIEfCLRNFn66vwGw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_4r8QgRuIEfCLRNFn66vwGw" keySequence="CTRL+{" command="_4q7j6xuIEfCLRNFn66vwGw">
      <parameters xmi:id="_4r83kBuIEfCLRNFn66vwGw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_4r83mhuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_LEFT" command="_4q35ixuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eoBuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_RIGHT" command="_4q8yBhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eqBuIEfCLRNFn66vwGw" keySequence="SHIFT+F2" command="_4q_OTRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eqRuIEfCLRNFn66vwGw" keySequence="SHIFT+F5" command="_4q-ALRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9eqhuIEfCLRNFn66vwGw" keySequence="ALT+F7" command="_4q-nfBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9erRuIEfCLRNFn66vwGw" keySequence="ALT+F5" command="_4q9ZLBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FsBuIEfCLRNFn66vwGw" keySequence="F11" command="_4rBqlhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FsRuIEfCLRNFn66vwGw" keySequence="F12" command="_4q_1jxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FshuIEfCLRNFn66vwGw" keySequence="CTRL+&#xc8;" command="_4q7j6xuIEfCLRNFn66vwGw">
      <tags>locale:fr</tags>
      <parameters xmi:id="_4r-FsxuIEfCLRNFn66vwGw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_4r-FuxuIEfCLRNFn66vwGw" keySequence="F2" command="_4q3SchuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FvhuIEfCLRNFn66vwGw" keySequence="F3" command="_4q681huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FwRuIEfCLRNFn66vwGw" keySequence="F4" command="_4q3SexuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FxxuIEfCLRNFn66vwGw" keySequence="F5" command="_4q8yDxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FyhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F7" command="_4rBqmhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FyxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F8" command="_4q7j6huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FzRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F9" command="_4q9ZKRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-swRuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+ARROW_LEFT" command="_4rBDhBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-swhuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_4q5uuxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-sxBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F4" command="_4q8LAxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-sxRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F6" command="_4q_OZhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T0BuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X J" command="_4q_OkBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T0huIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X M" command="_4q-AIhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T0xuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X A" command="_4q2rbxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T1BuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X B" command="_4rCRwhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T1RuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X E" command="_4q_OcRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T1huIEfCLRNFn66vwGw" keySequence="CTRL+F7" command="_4q-nURuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_64xuIEfCLRNFn66vwGw" keySequence="CTRL+F8" command="_4q683xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh8xuIEfCLRNFn66vwGw" keySequence="CTRL+F9" command="_4q5uxhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh-BuIEfCLRNFn66vwGw" keySequence="CTRL+F11" command="_4rBDmBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh-xuIEfCLRNFn66vwGw" keySequence="CTRL+F12" command="_4q5utBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJBxuIEfCLRNFn66vwGw" keySequence="CTRL+F4" command="_4q-AUBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJCRuIEfCLRNFn66vwGw" keySequence="CTRL+F6" command="_4q5HphuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJChuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+F7" command="_4q_OnhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJCxuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+X G" command="_4rBqkhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwHRuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_4q_OSBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwHhuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_4rCRrhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwHxuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_4q-nkRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwIBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+B D" command="_4rBqphuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwIRuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+B F" command="_4q6V1BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwIhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X O" command="_4q_OThuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwJRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X P" command="_4rBqsBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwJhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_4q_OXBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwKBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X Q" command="_4q6VzhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwKRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X R" command="_4q-ARRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBwKhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+X T" command="_4q9ZMRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-MBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_4q7j7RuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-MhuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+B R" command="_4q-nlhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-MxuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+B S" command="_4q_1eBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-NBuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+SHIFT+F12" command="_4rBqqxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-NRuIEfCLRNFn66vwGw" keySequence="DEL" command="_4q5HqxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-SxuIEfCLRNFn66vwGw" keySequence="ALT+-" command="_4q-nYxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlQBuIEfCLRNFn66vwGw" keySequence="ALT+CR" command="_4q_1exuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlQRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D B" command="_4rBqoBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlQhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D E" command="_4rCRtxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlQxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D A" command="_4rAcbBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlRBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D R" command="_4q-ARxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlRRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D T" command="_4q2EYxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlRhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D J" command="_4q_1ZhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlRxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D O" command="_4q-nVRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlSBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D P" command="_4rAcehuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sDlSRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+D Q" command="_4q-ANBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rwDQRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_4rHxNBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rwDQhuIEfCLRNFn66vwGw" keySequence="CTRL+CR" command="_4q7j-BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3YDBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+C" command="_4q-nNxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NRxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+R" command="_4q9ZHxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50UBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+U" command="_4q_1VRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bVhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+I" command="_4q9ZFhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83lRuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_UP" command="_4q_OehuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83mBuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_DOWN" command="_4q8K8BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9epBuIEfCLRNFn66vwGw" keySequence="SHIFT+INSERT" command="_4q5HqBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FtRuIEfCLRNFn66vwGw" keySequence="INSERT" command="_4q8yIBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FwxuIEfCLRNFn66vwGw" keySequence="F4" command="_4q35hBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_64RuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_UP" command="_4q_1fxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh8RuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_4q9ZGxuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rwDRhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_4rHxMhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rwqUBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+P" command="_4q_OnRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4rx4dRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+T" command="_4q8LBBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4ryfhBuIEfCLRNFn66vwGw" keySequence="CTRL+7" command="_4q-naBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r0UtBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+M" command="_4q686BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07wRuIEfCLRNFn66vwGw" keySequence="CTRL+/" command="_4q-naBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i1RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+C" command="_4q-naBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J4BuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F" command="_4rBqhBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3YBBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+B" command="_4rCRthuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_ExuIEfCLRNFn66vwGw" keySequence="CTRL+T" command="_4q-ncRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mIhuIEfCLRNFn66vwGw" keySequence="CTRL+I" command="_4q8yBBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NNhuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4q9ZNBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NPRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+/" command="_4q-AQhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NRRuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+R" command="_4q-ASxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50TxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+U" command="_4q_1bRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bUBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+'" command="_4q-ngxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CYxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+O" command="_4q8K-huIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83kRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+\" command="_4q4gmRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-sxhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+ARROW_UP" command="_4q-AUxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-sxxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_4q-AIBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_T1xuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_UP" command="_4q-AMxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_65BuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_4q5uthuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh9huIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_4q8yDRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh-RuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_4q4glBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJBhuIEfCLRNFn66vwGw" keySequence="CTRL+F3" command="_4rCRkRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-OxuIEfCLRNFn66vwGw" keySequence="CTRL+2 F" command="_4rBqqBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-RxuIEfCLRNFn66vwGw" keySequence="CTRL+2 R" command="_4q_1fRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-SBuIEfCLRNFn66vwGw" keySequence="CTRL+2 T" command="_4q-nZxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-SRuIEfCLRNFn66vwGw" keySequence="CTRL+2 L" command="_4q35jhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-ShuIEfCLRNFn66vwGw" keySequence="CTRL+2 M" command="_4q8yEhuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rwqUhuIEfCLRNFn66vwGw" elementId="org.eclipse.core.runtime.xml" bindingContext="_4rwqURuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rwqUxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+P" command="_4q_1kBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i3RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+D" command="_4q-AQBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rwqVBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_4rHxLBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rwqVRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+P" command="_4q8yHRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J5huIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+G" command="_4rBDlhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J6BuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+H" command="_4rAcYRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NRhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+R" command="_4q3SchuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FvxuIEfCLRNFn66vwGw" keySequence="F3" command="_4rBDhRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FwhuIEfCLRNFn66vwGw" keySequence="F4" command="_4q2EUxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_64BuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_UP" command="_4q-ndhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh8BuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_4q-nehuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rwqVhuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_4rHxOBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rwqVxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+P" command="_4q-ncxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i0RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+A" command="_4rCRxBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i2RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+C" command="_4rBqghuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r2J5BuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F" command="_4rCRoRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r4mIxuIEfCLRNFn66vwGw" keySequence="CTRL+I" command="_4rBDdhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NPBuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4q-nghuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NPxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+/" command="_4q-nYhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83kxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+\" command="_4q_OcxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FwBuIEfCLRNFn66vwGw" keySequence="F3" command="_4q-ncBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r_64huIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_UP" command="_4q_OkxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh8huIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_4q_1VxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh9xuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_4q3SgxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sAh-huIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_4q_OUBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rxRZRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_4rHxLRuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rxRZhuIEfCLRNFn66vwGw" keySequence="CTRL+1" command="_4rCRnRuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rx4eRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.serverViewScope" bindingContext="_4rHxKxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4rx4ehuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+D" command="_4q_OixuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07yRuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+P" command="_4q_1URuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i0xuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+R" command="_4rCRlBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i2xuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+S" command="_4q6V4huIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4rx4fxuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_4rHKEBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4ryfgBuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+V" command="_4q8yGxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i1xuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+C" command="_4q_1chuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83lhuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_UP" command="_4q2EVRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r83mxuIEfCLRNFn66vwGw" keySequence="ALT+ARROW_RIGHT" command="_4rBqlRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9epRuIEfCLRNFn66vwGw" keySequence="SHIFT+INSERT" command="_4q8yGxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJARuIEfCLRNFn66vwGw" keySequence="CTRL+INSERT" command="_4q_1chuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4ryfhRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_4rHxPxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4ryfhhuIEfCLRNFn66vwGw" keySequence="CTRL+7" command="_4q-naBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r07whuIEfCLRNFn66vwGw" keySequence="CTRL+/" command="_4q-naBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r1i1huIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+C" command="_4q-naBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r0UtRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_4rHxNRuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r0UthuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+M" command="_4q2EchuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_EBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+C" command="_4q-nNxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NOBuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4rBqsxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NSBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+R" command="_4q9ZHxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50SBuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+S" command="_4q8LChuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r50URuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+U" command="_4q_1VRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bVxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+I" command="_4q9ZFhuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r07xRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_4rHxPBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r07xhuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+O" command="_4q2rZxuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r1i4BuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_4rHxOxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r1i4RuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F" command="_4rBqhBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NQxuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+R" command="_4q3SehuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7CYhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+O" command="_4q2EXxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9epxuIEfCLRNFn66vwGw" keySequence="SHIFT+F2" command="_4q-nhRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FvRuIEfCLRNFn66vwGw" keySequence="F3" command="_4q2EZhuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r2J4huIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_4rHxJhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r2J4xuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+F" command="_4q4gkhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NOxuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4q5HoBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r2w9RuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_4rHxKhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r2w9huIEfCLRNFn66vwGw" keySequence="ALT+CTRL+M" command="_4q9ZLhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3YABuIEfCLRNFn66vwGw" keySequence="ALT+CTRL+N" command="_4rBqrBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_EhuIEfCLRNFn66vwGw" keySequence="CTRL+T" command="_4q686xuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r3_GBuIEfCLRNFn66vwGw" keySequence="CTRL+W" command="_4q_OQRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NMhuIEfCLRNFn66vwGw" keySequence="CTRL+N" command="_4q_ObBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r3YBRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_4rHxMRuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r3YBhuIEfCLRNFn66vwGw" keySequence="ALT+SHIFT+B" command="_4rCRthuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r3YBxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_4rHxLhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r3YCBuIEfCLRNFn66vwGw" keySequence="CTRL+R" command="_4q-nVBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9erhuIEfCLRNFn66vwGw" keySequence="F7" command="_4rCRlxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r9erxuIEfCLRNFn66vwGw" keySequence="F8" command="_4q-njxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FxhuIEfCLRNFn66vwGw" keySequence="F5" command="_4q3SgBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r-FyBuIEfCLRNFn66vwGw" keySequence="F6" command="_4q9ZIhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJBRuIEfCLRNFn66vwGw" keySequence="CTRL+F2" command="_4q_1lRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJCBuIEfCLRNFn66vwGw" keySequence="CTRL+F5" command="_4rBqkRuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r3YCRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.console" bindingContext="_4rHxJBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r3YChuIEfCLRNFn66vwGw" keySequence="CTRL+R" command="_4q-nfhuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r7pcRuIEfCLRNFn66vwGw" keySequence="CTRL+Z" command="_4rBqthuIEfCLRNFn66vwGw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_4r4mJhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_4rHxLxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r4mJxuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+," command="_4rBDhxuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r5NMRuIEfCLRNFn66vwGw" keySequence="CTRL+SHIFT+." command="_4q_1hRuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4r6bURuIEfCLRNFn66vwGw" keySequence="CTRL+G" command="_4q_1hhuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r5NNBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_4rHxIhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r5NNRuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4q-AMhuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r5NORuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_4rHxNxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r5NOhuIEfCLRNFn66vwGw" keySequence="CTRL+O" command="_4q2rZxuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r50QhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_4rHxQBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r50QxuIEfCLRNFn66vwGw" keySequence="CTRL+C" command="_4q688BuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sBJDhuIEfCLRNFn66vwGw" keySequence="CTRL+ARROW_LEFT" command="_4q4glRuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r50RBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_4rHxMBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r50RRuIEfCLRNFn66vwGw" keySequence="CTRL+C" command="_4q5HpxuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r-FtxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_4rHxNhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r-FuBuIEfCLRNFn66vwGw" keySequence="F1" command="_4q2EZBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r-FuRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_4rHxQRuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r-FuhuIEfCLRNFn66vwGw" keySequence="F2" command="_4q5HrhuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4r-FxBuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_4rHxOhuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4r-FxRuIEfCLRNFn66vwGw" keySequence="F5" command="_4rBqgRuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4sBJAxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_4rHxKRuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4sBJBBuIEfCLRNFn66vwGw" keySequence="CTRL+INSERT" command="_4q_OVxuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4sBwIxuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_4rHxMxuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4sBwJBuIEfCLRNFn66vwGw" keySequence="ALT+Y" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-NhuIEfCLRNFn66vwGw" keySequence="ALT+A" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-NxuIEfCLRNFn66vwGw" keySequence="ALT+B" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-OBuIEfCLRNFn66vwGw" keySequence="ALT+C" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-ORuIEfCLRNFn66vwGw" keySequence="ALT+D" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-OhuIEfCLRNFn66vwGw" keySequence="ALT+E" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-PBuIEfCLRNFn66vwGw" keySequence="ALT+F" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-PRuIEfCLRNFn66vwGw" keySequence="ALT+G" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-PhuIEfCLRNFn66vwGw" keySequence="ALT+P" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-PxuIEfCLRNFn66vwGw" keySequence="ALT+R" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-QBuIEfCLRNFn66vwGw" keySequence="ALT+S" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-QRuIEfCLRNFn66vwGw" keySequence="ALT+T" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-QhuIEfCLRNFn66vwGw" keySequence="ALT+V" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-QxuIEfCLRNFn66vwGw" keySequence="ALT+W" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-RBuIEfCLRNFn66vwGw" keySequence="ALT+H" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-RRuIEfCLRNFn66vwGw" keySequence="ALT+L" command="_4q-nYBuIEfCLRNFn66vwGw"/>
    <bindings xmi:id="_4sC-RhuIEfCLRNFn66vwGw" keySequence="ALT+N" command="_4q-nYBuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_4sC-ThuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_4rHxIBuIEfCLRNFn66vwGw">
    <bindings xmi:id="_4sC-TxuIEfCLRNFn66vwGw" keySequence="ALT+CR" command="_4q-nQxuIEfCLRNFn66vwGw"/>
  </bindingTables>
  <bindingTables xmi:id="_5OI-oRuIEfCLRNFn66vwGw" bindingContext="_5OI-oBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OKMwBuIEfCLRNFn66vwGw" bindingContext="_5OJlsBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OKMwhuIEfCLRNFn66vwGw" bindingContext="_5OKMwRuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OKz0RuIEfCLRNFn66vwGw" bindingContext="_5OKz0BuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OKz0xuIEfCLRNFn66vwGw" bindingContext="_5OKz0huIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OLa4RuIEfCLRNFn66vwGw" bindingContext="_5OLa4BuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OLa4xuIEfCLRNFn66vwGw" bindingContext="_5OLa4huIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OMB8RuIEfCLRNFn66vwGw" bindingContext="_5OMB8BuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OMB8xuIEfCLRNFn66vwGw" bindingContext="_5OMB8huIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OMB9RuIEfCLRNFn66vwGw" bindingContext="_5OMB9BuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OMpARuIEfCLRNFn66vwGw" bindingContext="_5OMpABuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OMpAxuIEfCLRNFn66vwGw" bindingContext="_5OMpAhuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OMpBRuIEfCLRNFn66vwGw" bindingContext="_5OMpBBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ONQEBuIEfCLRNFn66vwGw" bindingContext="_5OMpBhuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ONQEhuIEfCLRNFn66vwGw" bindingContext="_5ONQERuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ONQFBuIEfCLRNFn66vwGw" bindingContext="_5ONQExuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ONQFhuIEfCLRNFn66vwGw" bindingContext="_5ONQFRuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ONQGBuIEfCLRNFn66vwGw" bindingContext="_5ONQFxuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ONQGhuIEfCLRNFn66vwGw" bindingContext="_5ONQGRuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ON3IRuIEfCLRNFn66vwGw" bindingContext="_5ON3IBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5ON3IxuIEfCLRNFn66vwGw" bindingContext="_5ON3IhuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OOeMRuIEfCLRNFn66vwGw" bindingContext="_5OOeMBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OOeMxuIEfCLRNFn66vwGw" bindingContext="_5OOeMhuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OOeNRuIEfCLRNFn66vwGw" bindingContext="_5OOeNBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OPFQRuIEfCLRNFn66vwGw" bindingContext="_5OPFQBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OPFQxuIEfCLRNFn66vwGw" bindingContext="_5OPFQhuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OPFRRuIEfCLRNFn66vwGw" bindingContext="_5OPFRBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OPsURuIEfCLRNFn66vwGw" bindingContext="_5OPsUBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OPsUxuIEfCLRNFn66vwGw" bindingContext="_5OPsUhuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQTYBuIEfCLRNFn66vwGw" bindingContext="_5OPsVBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQTYhuIEfCLRNFn66vwGw" bindingContext="_5OQTYRuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQTZBuIEfCLRNFn66vwGw" bindingContext="_5OQTYxuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQTZhuIEfCLRNFn66vwGw" bindingContext="_5OQTZRuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQTaBuIEfCLRNFn66vwGw" bindingContext="_5OQTZxuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQ6cRuIEfCLRNFn66vwGw" bindingContext="_5OQ6cBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQ6cxuIEfCLRNFn66vwGw" bindingContext="_5OQ6chuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQ6dRuIEfCLRNFn66vwGw" bindingContext="_5OQ6dBuIEfCLRNFn66vwGw"/>
  <bindingTables xmi:id="_5OQ6dxuIEfCLRNFn66vwGw" bindingContext="_5OQ6dhuIEfCLRNFn66vwGw"/>
  <rootContext xmi:id="_4pvRGhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_4pvRGxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_4pvRHBuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_4rHKEBuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_4rHxIBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_4rHxIRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_4rHxIhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_4rHxJhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_4rHxLBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_4rHxLRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_4rHxMhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_4rHxNRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_4rHxNhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_4rHxNxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_4rHxPBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_4rHxOBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_4rHxORuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_4rHxPRuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_4rHxOxuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_4rHxPxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_4rHxJBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_4rHxJRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_4rHxJxuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_4rHxKRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_4rHxKhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_4rHxKxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_4rHxLhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_4rHxLxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_4rHxPhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_4rHxMBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_4rHxMxuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_4rHxNBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_4rHxOhuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_4rHxQBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_4rHxQRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_4pvRHRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_4rHxIxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_4rHxKBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_4rHxMRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_4rwqURuIEfCLRNFn66vwGw" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_5OI-oBuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_5OJlsBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_5OKMwRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_5OKz0BuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_5OKz0huIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_5OLa4BuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_5OLa4huIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_5OMB8BuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_5OMB8huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_5OMB9BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_5OMpABuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_5OMpAhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_5OMpBBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_5OMpBhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_5ONQERuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_5ONQExuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_5ONQFRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_5ONQFxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_5ONQGRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_5ON3IBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_5ON3IhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_5OOeMBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_5OOeMhuIEfCLRNFn66vwGw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_5OOeNBuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_5OPFQBuIEfCLRNFn66vwGw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_5OPFQhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_5OPFRBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_5OPsUBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_5OPsUhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_5OPsVBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_5OQTYRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_5OQTYxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_5OQTZRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_5OQTZxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_5OQ6cBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_5OQ6chuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_5OQ6dBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_5OQ6dhuIEfCLRNFn66vwGw" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <descriptors xmi:id="_4u-WYBuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_4_F2ABuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_4_HEIBuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_4_HrMBuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_4_HrMRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_HrMhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_ISQBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_I5UBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_I5URuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_I5UhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_JgYBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_JgYRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_JgYhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_4_KHcBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_4_KHcRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_4_KHchuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_4_KugBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_4_KugRuIEfCLRNFn66vwGw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_4_LVkBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_L8oBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_L8oRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_MjsBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_MjsRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_Nx0BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_Nx0RuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_4_OY4BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_4_OY4RuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_4_OY4huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_4_O_8BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_O_8RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_PnABuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_PnARuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_4_PnAhuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_4_PnAxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_4_PnBBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_4_QOEBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_4_QOERuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_4_Q1IBuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_4_Q1IRuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_4_Q1IhuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_4_Q1IxuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_4_RcMBuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_4_RcMRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_4_RcMhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_4_SDQBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_4_SDQRuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_4_SDQhuIEfCLRNFn66vwGw" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_4_SqUBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_4_SqURuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_4_SqUhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_4_SqUxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_4_TRYBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_4_T4cBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_4_T4cRuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_UfgBuIEfCLRNFn66vwGw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_4_VGkBuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_4_VGkRuIEfCLRNFn66vwGw" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_4_VGkhuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_4_VtoBuIEfCLRNFn66vwGw" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_4_VtoRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_VtohuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_VtoxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_4_W7wBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_Xi0BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_YJ4BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_ZYABuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_ZYARuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_ZYAhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_Z_EBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_Z_ERuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_Z_EhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_Z_ExuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_amIBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_4_amIRuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_4_amIhuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_4_bNMBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_4_bNMRuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_4q02MBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q02MRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q02MhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q1dQBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q1dQRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_4q2EUBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EURuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EUhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EUxuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EVBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EVRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_4qyZ9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EVhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EVxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EWBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EWRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EWhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EWxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EXBuIEfCLRNFn66vwGw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q2EXRuIEfCLRNFn66vwGw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_4q2EXhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EXxuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EYBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EYRuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_4qyZ8BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EYhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EYxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EZBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EZRuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_4qyZ-RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EZhuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EZxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EaBuIEfCLRNFn66vwGw" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_4qxy4xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EaRuIEfCLRNFn66vwGw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EahuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EaxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EbBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EbRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EbhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EbxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EcBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EcRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EchuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_4qxy5xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2EcxuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rYBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rYRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rYhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q2rYxuIEfCLRNFn66vwGw" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_4q2rZBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rZRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rZhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rZxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2raBuIEfCLRNFn66vwGw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_4qxy7huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2raRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rahuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2raxuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rbBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rbRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rbhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q2rbxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3ScBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3ScRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SchuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3ScxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SdBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SdRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SdhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SdxuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SeBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SeRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SehuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SexuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SfBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SfRuIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_4qyZ-huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SfhuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SfxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SgBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SgRuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SghuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3SgxuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3ShBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q3ShRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35gBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35gRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35ghuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q35gxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_4q35hBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35hRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35hhuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_4qxy8xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35hxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35iBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35iRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35ihuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35ixuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35jBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35jRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35jhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35jxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q35kBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gkBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gkRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gkhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_4qyZ_huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gkxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4glBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4glRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4glhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4glxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gmBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gmRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gmhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gmxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gnBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gnRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q4gnhuIEfCLRNFn66vwGw" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_4q4gnxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4goBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4goRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4gohuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q4goxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q4gpBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_4q4gpRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HoBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HoRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HohuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HoxuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_4qxy4BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HpBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HpRuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HphuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HpxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HqBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HqRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HqhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HqxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HrBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HrRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HrhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HrxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5HsBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5usBuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q5usRuIEfCLRNFn66vwGw" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_4q5ushuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5usxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5utBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5utRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uthuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5utxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_4qyZ_RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uuBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uuRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uuhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uuxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uvBuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uvRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uvhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uvxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_4qxy8huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q5uwBuIEfCLRNFn66vwGw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_4q5uwRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uwhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uwxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uxBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uxRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uxhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uxxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uyBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uyRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uyhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q5uyxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VwBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VwRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VwhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VwxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VxBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_4qyZ-RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VxRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VxhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VxxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VyBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VyRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VyhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VyxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VzBuIEfCLRNFn66vwGw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q6VzRuIEfCLRNFn66vwGw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_4q6VzhuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6VzxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V0BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V0RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V0huIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V0xuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V1BuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V1RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V1huIEfCLRNFn66vwGw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V1xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V2BuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V2RuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V2huIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V2xuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V3BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V3RuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V3huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V3xuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V4BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_4qxy8BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V4RuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V4huIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_4qxy7xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q6V4xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_4qxy-BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q680BuIEfCLRNFn66vwGw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_4q680RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q680huIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q680xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q681BuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q681RuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q681huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q681xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q682BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q682RuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q682huIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q682xuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q683BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q683RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q683huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q683xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q684BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q684RuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q684huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q684xuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_4qyZ-BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q685BuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_4q685RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q685huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q685xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q686BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q686RuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q686huIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_4qxy5xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q686xuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q687BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q687RuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q687huIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q687xuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q688BuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j4BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j4RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_4qxy9huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q7j4huIEfCLRNFn66vwGw" elementId="url" name="URL"/>
    <parameters xmi:id="_4q7j4xuIEfCLRNFn66vwGw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_4q7j5BuIEfCLRNFn66vwGw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_4q7j5RuIEfCLRNFn66vwGw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_4q7j5huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j5xuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j6BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j6RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j6huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j6xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_4qxy9huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q7j7BuIEfCLRNFn66vwGw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_4q7j7RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j7huIEfCLRNFn66vwGw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j7xuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j8BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j8RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j8huIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j8xuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j9BuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_4qxy4huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j9RuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j9huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j9xuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j-BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j-RuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j-huIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q7j-xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K8BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K8RuIEfCLRNFn66vwGw" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_4qxy4xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K8huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K8xuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K9BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K9RuIEfCLRNFn66vwGw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_4qxy7huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K9huIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K9xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K-BuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_4qxy5RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K-RuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K-huIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K-xuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K_BuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K_RuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K_huIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8K_xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LABuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LARuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LAhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LAxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LBBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LBRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LBhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LBxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LCBuIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LCRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LChuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_4qxy5xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LCxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LDBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LDRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LDhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8LDxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_4qxy8huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q8yABuIEfCLRNFn66vwGw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_4q8yARuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yAhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yAxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_4qyZ-RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yBBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yBRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yBhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yBxuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yCBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yCRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yChuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yCxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yDBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yDRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yDhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yDxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yEBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yERuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yEhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yExuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yFBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yFRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yFhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yFxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yGBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yGRuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_4qyZ8BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yGhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yGxuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_4qyZ9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yHBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yHRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q8yHhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q8yHxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_4q8yIBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZEBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZERuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZEhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZExuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZFBuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZFRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZFhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZFxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZGBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZGRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZGhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZGxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZHBuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_4qxy4BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZHRuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZHhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZHxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZIBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZIRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZIhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZIxuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZJBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_4qxy8huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q9ZJRuIEfCLRNFn66vwGw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_4q9ZJhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZJxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZKBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZKRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZKhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZKxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZLBuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZLRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZLhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZLxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZMBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZMRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZMhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZMxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q9ZNBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AIBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AIRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AIhuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AIxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AJBuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AJRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AJhuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AJxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AKBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AKRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AKhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AKxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ALBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ALRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ALhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ALxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AMBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AMRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_4qyZ_BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AMhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AMxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ANBuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ANRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ANhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_4qxy9huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-ANxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_4q-AOBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AORuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AOhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_4qxy-BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-AOxuIEfCLRNFn66vwGw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_4q-APBuIEfCLRNFn66vwGw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_4q-APRuIEfCLRNFn66vwGw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_4q-APhuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-APxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AQBuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AQRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AQhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AQxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ARBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ARRuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ARhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ARxuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ASBuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ASRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AShuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ASxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ATBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ATRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_4qxy8xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AThuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ATxuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AUBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AURuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AUhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AUxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AVBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-AVRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_4q-AVhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AVxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AWBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-AWRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_4q-AWhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-AWxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nMBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_4qyZ9xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-nMRuIEfCLRNFn66vwGw" elementId="title" name="Title"/>
    <parameters xmi:id="_4q-nMhuIEfCLRNFn66vwGw" elementId="message" name="Message"/>
    <parameters xmi:id="_4q-nMxuIEfCLRNFn66vwGw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_4q-nNBuIEfCLRNFn66vwGw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_4q-nNRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nNhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nNxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nOBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nORuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nOhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nOxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nPBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nPRuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nPhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nPxuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nQBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nQRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nQhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nQxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nRBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nRRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nRhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nRxuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_4qxy6BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nSBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nSRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nShuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nSxuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_4qxy8xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nTBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_4qxy9huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-nTRuIEfCLRNFn66vwGw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_4q-nThuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nTxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nUBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nURuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nUhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nUxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nVBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nVRuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nVhuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nVxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nWBuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_4qyZ8BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nWRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nWhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nWxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nXBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nXRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nXhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nXxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nYBuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_4qyZ9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nYRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nYhuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nYxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nZBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nZRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nZhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nZxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-naBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-naRuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nahuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-naxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nbBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nbRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nbhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nbxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ncBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ncRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nchuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ncxuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ndBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ndRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ndhuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ndxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-neBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-neRuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_4qxy5RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nehuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nexuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nfBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nfRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nfhuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nfxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_4qyZ-BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-ngBuIEfCLRNFn66vwGw" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_4q-ngRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nghuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-ngxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nhBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nhRuIEfCLRNFn66vwGw" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nhhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nhxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-niBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-niRuIEfCLRNFn66vwGw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_4qxy9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nihuIEfCLRNFn66vwGw" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_4qxy4xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nixuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-njBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-njRuIEfCLRNFn66vwGw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-njhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-njxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nkBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nkRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nkhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nkxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nlBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nlRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nlhuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nlxuIEfCLRNFn66vwGw" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q-nmBuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_4q-nmRuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_4q-nmhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nmxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nnBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nnRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q-nnhuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OQBuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_4q_OQRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OQhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OQxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ORBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ORRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ORhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ORxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OSBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OSRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OShuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OSxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OTBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OTRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OThuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OTxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OUBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OURuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OUhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OUxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OVBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OVRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_4qyZ-RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OVhuIEfCLRNFn66vwGw" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OVxuIEfCLRNFn66vwGw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_4qxy7huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OWBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OWRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_4qxy5BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OWhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_4q_OWxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OXBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OXRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OXhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OXxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OYBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_4qxy5RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OYRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OYhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OYxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OZBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OZRuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OZhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OZxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OaBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OaRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OahuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OaxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ObBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ObRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ObhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_ObxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OcBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OcRuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OchuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OcxuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OdBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_4qyZ_RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OdRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OdhuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OdxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OeBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OeRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OehuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OexuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OfBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_4qxy5RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OfRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_4qxy4RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OfhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_4q_OfxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_4q_OgBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_4q_OgRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OghuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OgxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OhBuIEfCLRNFn66vwGw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_4q_OhRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OhhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OhxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OiBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OiRuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_4qxy5RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OihuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OixuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_4qxy7xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OjBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OjRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OjhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_4qxy7BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OjxuIEfCLRNFn66vwGw" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_4q_OkBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OkRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OkhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_4qyZ_huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OkxuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OlBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_4qxy-BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OlRuIEfCLRNFn66vwGw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_4q_OlhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OlxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OmBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OmRuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OmhuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_OmxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_4q_OnBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OnRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OnhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OnxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_OoBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1UBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1URuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_4qxy7xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1UhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1UxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1VBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1VRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1VhuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1VxuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1WBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1WRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_4qxy8xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1WhuIEfCLRNFn66vwGw" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_4qyZ8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1WxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1XBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1XRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1XhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1XxuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1YBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1YRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1YhuIEfCLRNFn66vwGw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1YxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ZBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ZRuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ZhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ZxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1aBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1aRuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ahuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1axuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1bBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1bRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1bhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1bxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1cBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1cRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1chuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_4qyZ9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1cxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1dBuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1dRuIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_4qyZ-huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4q_1dhuIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_4q_1dxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1eBuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1eRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ehuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1exuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1fBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1fRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1fhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1fxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1gBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1gRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ghuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1gxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1hBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1hRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1hhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1hxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1iBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1iRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ihuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1ixuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1jBuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1jRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1jhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1jxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1kBuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1kRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1khuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1kxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1lBuIEfCLRNFn66vwGw" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_4qyZ8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1lRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4q_1lhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcYBuIEfCLRNFn66vwGw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcYRuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcYhuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcYxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcZBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcZRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcZhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcZxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_4qyZ-RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rAcaBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_4rAcaRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_4rAcahuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcaxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcbBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcbRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcbhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcbxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAccBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAccRuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcchuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAccxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcdBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcdRuIEfCLRNFn66vwGw" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcdhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcdxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAceBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAceRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcehuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcexuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rAcfBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDcBuIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_4qyZ-huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDcRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDchuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDcxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDdBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDdRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDdhuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDdxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDeBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDeRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDehuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDexuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDfBuIEfCLRNFn66vwGw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDfRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDfhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDfxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDgBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDgRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDghuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDgxuIEfCLRNFn66vwGw" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDhBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDhRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDhhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDhxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDiBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDiRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDihuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDixuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDjBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_4qyZ9xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rBDjRuIEfCLRNFn66vwGw" elementId="title" name="Title"/>
    <parameters xmi:id="_4rBDjhuIEfCLRNFn66vwGw" elementId="message" name="Message"/>
    <parameters xmi:id="_4rBDjxuIEfCLRNFn66vwGw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_4rBDkBuIEfCLRNFn66vwGw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_4rBDkRuIEfCLRNFn66vwGw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_4rBDkhuIEfCLRNFn66vwGw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_4rBDkxuIEfCLRNFn66vwGw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_4rBDlBuIEfCLRNFn66vwGw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_4rBDlRuIEfCLRNFn66vwGw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_4rBDlhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDlxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDmBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDmRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDmhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDmxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDnBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDnRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDnhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDnxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDoBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDoRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_4qyZ-RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDohuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDoxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDpBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDpRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDphuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rBDpxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_4rBDqBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_4rBDqRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDqhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDqxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDrBuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBDrRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rBDrhuIEfCLRNFn66vwGw" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_4rBqgBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqgRuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_4qxy4RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqghuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqgxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqhBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqhRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqhhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqhxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqiBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqiRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqihuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqixuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_4qxy4BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqjBuIEfCLRNFn66vwGw" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_4rBqjRuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_4rBqjhuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_4rBqjxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_4qyZ8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqkBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqkRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqkhuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqkxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqlBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqlRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_4qyZ9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqlhuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqlxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqmBuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqmRuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqmhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqmxuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqnBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqnRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqnhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqnxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqoBuIEfCLRNFn66vwGw" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqoRuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqohuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqoxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqpBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqpRuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_4qxy8xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqphuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqpxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqqBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqqRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqqhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqqxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqrBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqrRuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqrhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqrxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqsBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqsRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqshuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqsxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_4qxy7BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqtBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqtRuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_4qxy6BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqthuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBqtxuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBquBuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBquRuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_4qxy8RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBquhuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rBquxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRkBuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRkRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRkhuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRkxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRlBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_4qxy7xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRlRuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRlhuIEfCLRNFn66vwGw" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_4rCRlxuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRmBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_4qxy9huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRmRuIEfCLRNFn66vwGw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_4rCRmhuIEfCLRNFn66vwGw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_4rCRmxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_4qxy-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRnBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_4qxy8huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRnRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRnhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRnxuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRoBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRoRuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRohuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRoxuIEfCLRNFn66vwGw" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRpBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRpRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_4qxy8huIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRphuIEfCLRNFn66vwGw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_4rCRpxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRqBuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRqRuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRqhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRqxuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRrBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRrRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRrhuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_4qxy6xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRrxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRsBuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRsRuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_4qyZ9RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRshuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRsxuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRtBuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_4rCRtRuIEfCLRNFn66vwGw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_4rCRthuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_4qxy7RuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRtxuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRuBuIEfCLRNFn66vwGw" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_4qxy-BuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRuRuIEfCLRNFn66vwGw" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_4rCRuhuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_4qxy9xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRuxuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_4qyZ-xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRvBuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_4qxy9BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRvRuIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRvhuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_4qyZ-BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRvxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRwBuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRwRuIEfCLRNFn66vwGw" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRwhuIEfCLRNFn66vwGw" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_4qyZ9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRwxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_4qxy6huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRxBuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_4qxy5BuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRxRuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_4qxy9huIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRxhuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4rCRxxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_4qxy7RuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRyBuIEfCLRNFn66vwGw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_4rCRyRuIEfCLRNFn66vwGw" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_4qyZ_xuIEfCLRNFn66vwGw">
    <parameters xmi:id="_4rCRyhuIEfCLRNFn66vwGw" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_4rCRyxuIEfCLRNFn66vwGw" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_4v15EBuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_4-fZEBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-gnMBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-gnMRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-gnMhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hOQBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hOQRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hOQhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hOQxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hORBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hORRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-hORhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jDcBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jDcRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jDchuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jDcxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqgBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqgRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqghuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqgxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqhBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqhRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqhhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqhxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqiBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-jqiRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRkBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRkRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRkhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRkxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRlBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRlRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRlhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRlxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-kRmBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-k4oBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-k4oRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-k4ohuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-lfsBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-lfsRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-lfshuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-lfsxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGwBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGwRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGwhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGwxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGxBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGxRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGxhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGxxuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGyBuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGyRuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mGyhuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt0BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt0RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt0huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt0xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt1BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt1RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt1huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt1xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt2BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt2RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt2huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt2xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt3BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-mt3RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU4BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU4RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU4huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU4xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU5BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU5RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU5huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU5xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU6BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU6RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU6huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU6xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU7BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU7RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU7huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU7xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU8BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-nU8RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n78BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n78RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n78huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n78xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n79BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n79RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n79huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n79xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7-BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7-RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7-huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7-xuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7_BuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7_RuIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <commands xmi:id="_4-n7_huIEfCLRNFn66vwGw" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_4qyZ_xuIEfCLRNFn66vwGw"/>
  <addons xmi:id="_4pvRHhuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_4pvRHxuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_4pvRIBuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_4pvRIRuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_4pvRIhuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_4pvRIxuIEfCLRNFn66vwGw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_4pvRJBuIEfCLRNFn66vwGw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_4pvRJRuIEfCLRNFn66vwGw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_4pvRJhuIEfCLRNFn66vwGw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_4pvRJxuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_4qEoQBuIEfCLRNFn66vwGw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_4qxy4BuIEfCLRNFn66vwGw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_4qxy4RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_4qxy4huIEfCLRNFn66vwGw" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_4qxy4xuIEfCLRNFn66vwGw" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_4qxy5BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_4qxy5RuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_4qxy5huIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_4qxy5xuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_4qxy6BuIEfCLRNFn66vwGw" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_4qxy6RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_4qxy6huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_4qxy6xuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_4qxy7BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_4qxy7RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_4qxy7huIEfCLRNFn66vwGw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_4qxy7xuIEfCLRNFn66vwGw" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_4qxy8BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_4qxy8RuIEfCLRNFn66vwGw" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_4qxy8huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_4qxy8xuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_4qxy9BuIEfCLRNFn66vwGw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_4qxy9RuIEfCLRNFn66vwGw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_4qxy9huIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_4qxy9xuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_4qxy-BuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_4qyZ8BuIEfCLRNFn66vwGw" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_4qyZ8RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_4qyZ8huIEfCLRNFn66vwGw" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_4qyZ8xuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_4qyZ9BuIEfCLRNFn66vwGw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_4qyZ9RuIEfCLRNFn66vwGw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_4qyZ9huIEfCLRNFn66vwGw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_4qyZ9xuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_4qyZ-BuIEfCLRNFn66vwGw" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_4qyZ-RuIEfCLRNFn66vwGw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_4qyZ-huIEfCLRNFn66vwGw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_4qyZ-xuIEfCLRNFn66vwGw" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_4qyZ_BuIEfCLRNFn66vwGw" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_4qyZ_RuIEfCLRNFn66vwGw" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_4qyZ_huIEfCLRNFn66vwGw" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_4qyZ_xuIEfCLRNFn66vwGw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
</application:Application>
