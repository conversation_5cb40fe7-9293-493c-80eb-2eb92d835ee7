package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)

public class BeginTransactionRequest {
    private List<String> offerIds;
    private String currency;
    private String culture;
    private String reservationNumber;

    // Constructeur par défaut
    public BeginTransactionRequest() {}

    // Constructeur avec paramètres
    public BeginTransactionRequest(List<String> offerIds, String currency, String culture, String reservationNumber) {
        this.offerIds = offerIds;
        this.currency = currency;
        this.culture = culture;
        this.reservationNumber = reservationNumber;
    }

    // Getters et Setters
    public List<String> getOfferIds() {
        return offerIds;
    }

    public void setOfferIds(List<String> offerIds) {
        this.offerIds = offerIds;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCulture() {
        return culture;
    }

    public void setCulture(String culture) {
        this.culture = culture;
    }

    public String getReservationNumber() {
        return reservationNumber;
    }

    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }
}